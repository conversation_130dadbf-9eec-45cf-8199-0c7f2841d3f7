/*  reset css style
		2024-4-17
   author: b<PERSON><PERSON><PERSON><PERSON>an
*/

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  font-family: Source Han Sans CN;
}
/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  /* min-width: 1680px; */
}
ol,
ul {
  list-style: none;
  padding: 0 !important;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
body {
  background-color: #f4f6f8;
}
::-webkit-scrollbar {
  width: 5px; /* 滚动条的宽度 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条的背景色 */
}

::-webkit-scrollbar-thumb {
  background: #ccc; /* 滚动条的滑块颜色 */
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa; /* 滚动条滑块悬停时的颜色 */
}


.scale-picker-panel {
  position: absolute !important;
}
