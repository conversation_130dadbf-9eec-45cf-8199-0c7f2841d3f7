<template>
  <div class="panel-tab__content">
    <el-form :model="flowConditionForm" label-width="90px" size="mini" @submit.native.prevent>
      <el-form-item label="流转类型">
        <el-select v-model="flowConditionForm.type" @change="updateFlowType">
          <el-option label="普通流转路径" value="normal" />
          <el-option label="默认流转路径" value="default" />
          <el-option label="条件流转路径" value="condition" />
        </el-select>
      </el-form-item>
      <el-form-item label="条件格式" v-if="flowConditionForm.type === 'condition'" key="condition">
        <el-select v-model="flowConditionForm.conditionType">
          <el-option label="表达式" value="expression" />
          <el-option label="脚本" value="script" />
        </el-select>
      </el-form-item>
      <el-form-item label="条件字段"
        v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'expression'" key="chooseField">
        <el-button type="success" @click="toChooseField">选择字段</el-button>
      </el-form-item>
      <el-form-item label="表达式"
        v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'expression'" key="express">
        <el-input v-model="flowConditionForm.body" clearable @change="updateFlowCondition" />
      </el-form-item>
      <template v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'script'">
        <el-form-item label="脚本语言" key="language">
          <el-input v-model="flowConditionForm.language" clearable @change="updateFlowCondition" />
        </el-form-item>
        <el-form-item label="脚本类型" key="scriptType">
          <el-select v-model="flowConditionForm.scriptType">
            <el-option label="内联脚本" value="inlineScript" />
            <el-option label="外部脚本" value="externalScript" />
          </el-select>
        </el-form-item>
        <el-form-item label="脚本" v-if="flowConditionForm.scriptType === 'inlineScript'" key="body">
          <el-input v-model="flowConditionForm.body" type="textarea" clearable @change="updateFlowCondition" />
        </el-form-item>
        <el-form-item label="资源地址" v-if="flowConditionForm.scriptType === 'externalScript'" key="resource">
          <el-input v-model="flowConditionForm.resource" clearable @change="updateFlowCondition" />
        </el-form-item>
      </template>
    </el-form>
    <el-dialog :visible.sync="visible" title="表单字段" append-to-body>
      <el-table :data="tableData" max-height="400px" @row-click="rowClick">
        <el-table-column prop="nodeName" label="节点"></el-table-column>
        <el-table-column prop="formName" label="表单"></el-table-column>
        <el-table-column prop="fieldName" label="字段名"></el-table-column>
        <el-table-column prop="fieldTag" label="字段授权(点击切换)">
          <template slot-scope="{row}">
            <el-tag>{{ row.fieldTag }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { is } from 'bpmn-js/lib/util/ModelUtil';
import { listForm } from "@/api/workflow/form";
import { Message } from 'element-ui';


export default {
  name: "FlowCondition",
  props: {
    businessObject: Object,
    type: String
  },
  data() {
    return {
      flowConditionForm: {},
      formOptions: [],
      visible: false,
      columns: [{ title: '表单', prop: 'formName' }, { title: '字段授权(点击切换)', prop: 'fieldTag' }],
      tableData: []
    };
  },
  watch: {
    businessObject: {
      immediate: true,
      handler() {
        this.$nextTick(() => this.resetFlowCondition());
      }
    }
  },
  mounted() {
    this.getFormList()
  },
  methods: {
    rowClick(row, column, event) {
      console.log(row)
      this.$set(this.flowConditionForm, "body", "${  " + `key_${row.nodeId}_${row.fieldId}` + " == '值' }");
      this.updateFlowCondition()
      this.visible = false
    },

    debugPrecedingTasksAndStarts(modeler, boId) {
      const registry = modeler.get('elementRegistry');
      const all = registry.getAll();

      // 1. 找到对应图元
      let gfx = registry.get(boId);
      if (!gfx) {
        gfx = all.find(el => el.businessObject.id === boId);
      }
      if (!gfx) {
        throw new Error(`找不到 businessObject.id = ${boId}`);
      }

      const bo = gfx.businessObject;
      let startNode;
      if (bo.$type === 'bpmn:SequenceFlow') {
        startNode = bo.sourceRef;
        console.log(`[debug] 这是 SequenceFlow，起点 sourceRef = ${startNode.id} (${startNode.$type})`);
      } else if (is(bo, 'bpmn:FlowNode')) {
        startNode = bo;
        console.log(`[debug] 这是 FlowNode 起点 = ${startNode.id} (${startNode.$type})`);
      } else {
        throw new Error(`${boId} 既不是 FlowNode，也不是 SequenceFlow`);
      }

      const visited = new Set();
      const resultNodes = [];

      // 先收集起点自己（如果是目标类型）
      if (startNode.$type === 'bpmn:UserTask' || startNode.$type === 'bpmn:StartEvent') {
        console.log(`>>> 收集到起点 ${startNode.$type}: ${startNode.id}`);
        visited.add(startNode.id);
        resultNodes.push(startNode);
      }

      function traverse(node) {
        console.log(`[debug] traverse 节点 = ${node.id} (${node.$type}), incoming count = ${node.incoming?.length || 0}`);
        (node.incoming || []).forEach(seq => {
          console.log(`  ↳ 发现 SequenceFlow ${seq.id}`);
          const up = seq.sourceRef;
          console.log(`     上游 = ${up.id} (${up.$type})`);
          if (!up || visited.has(up.id)) {
            console.log(`     （已访问或无效，跳过）`);
            return;
          }
          visited.add(up.id);

          // 只在这里收集 UserTask 或 StartEvent
          if (up.$type === 'bpmn:UserTask' || up.$type === 'bpmn:StartEvent') {
            console.log(`     >>> 收集到 ${up.$type}: ${up.id}`);
            resultNodes.push(up);
          }
          // 继续向上追溯
          traverse(up);
        });
      }

      traverse(startNode);
      console.log('[debug] 最终收集到的节点：', resultNodes.map(n => `${n.$type}(${n.id})`));
      return resultNodes;
    },
    getFormList() {
      listForm().then(response => this.formOptions = response.rows)
    },
    toChooseField() {
      console.log(this.businessObject, this.formOptions)
      let d = this.debugPrecedingTasksAndStarts(window.bpmnInstances.modeler, this.businessObject.id)
      console.log(d)
      this.tableData = []

      for (let s of d) {
        console.log(s.formKey)
        const current = this.formOptions.find(el => `key_${el.formId}` === s.formKey)
        if (current) {
          const content = JSON.parse(current.content)
          for (let field of content.fields) {
            this.tableData.push({ nodeId: s.id, nodeName: s.name, formName: current.formName, formId: current.formId, fieldId: field.__vModel__, fieldName: field.__config__.label, fieldTag: field.__config__.tagIcon })
          }
        }
      }
      this.visible = true

      return
      const formKey = d.get('formKey')
      if (!formKey) {
        Message.warning('前置节点未绑定表单')
        return
      }
      console.log(formKey)
      const current = this.formOptions.find(el => `key_${el.formId}` === formKey)
      if (!current) {
        Message.warning('前置节点绑定表单失效')
        return
      }
      console.log(current, 'current')
      console.log(d.get('formKey'))
      const content = JSON.parse(current.content)
      this.tableData = []
      for (let field of content.fields) {
        this.tableData.push({ formName: current.formName, formId: current.formId, fieldId: field.__vModel__, fieldName: field.__config__.label, fieldTag: field.__config__.tagIcon })
      }
      this.visible = true
      console.log(window.bpmnInstances.modeler.get("elementRegistry").getAll())
    },
    resetFlowCondition() {
      this.bpmnElement = window.bpmnInstances.bpmnElement;
      this.bpmnElementSource = this.bpmnElement.source;
      this.bpmnElementSourceRef = this.bpmnElement.businessObject.sourceRef;
      if (this.bpmnElementSourceRef && this.bpmnElementSourceRef.default && this.bpmnElementSourceRef.default.id === this.bpmnElement.id) {
        // 默认
        this.flowConditionForm = { type: "default" };
      } else if (!this.bpmnElement.businessObject.conditionExpression) {
        // 普通
        this.flowConditionForm = { type: "normal" };
      } else {
        // 带条件
        const conditionExpression = this.bpmnElement.businessObject.conditionExpression;
        this.flowConditionForm = { ...conditionExpression, type: "condition" };
        // resource 可直接标识 是否是外部资源脚本
        if (this.flowConditionForm.resource) {
          this.$set(this.flowConditionForm, "conditionType", "script");
          this.$set(this.flowConditionForm, "scriptType", "externalScript");
          return;
        }
        if (conditionExpression.language) {
          this.$set(this.flowConditionForm, "conditionType", "script");
          this.$set(this.flowConditionForm, "scriptType", "inlineScript");
          return;
        }
        this.$set(this.flowConditionForm, "conditionType", "expression");
      }
    },
    updateFlowType(flowType) {
      // 正常条件类
      if (flowType === "condition") {
        this.flowConditionRef = window.bpmnInstances.moddle.create("bpmn:FormalExpression");
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          conditionExpression: this.flowConditionRef
        });
        return;
      }
      // 默认路径
      if (flowType === "default") {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          conditionExpression: null
        });
        window.bpmnInstances.modeling.updateProperties(this.bpmnElementSource, {
          default: this.bpmnElement
        });
        return;
      }
      // 正常路径，如果来源节点的默认路径是当前连线时，清除父元素的默认路径配置
      if (this.bpmnElementSourceRef.default && this.bpmnElementSourceRef.default.id === this.bpmnElement.id) {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElementSource, {
          default: null
        });
      }
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        conditionExpression: null
      });
    },
    updateFlowCondition() {
      let { conditionType, scriptType, body, resource, language } = this.flowConditionForm;
      let condition;
      if (conditionType === "expression") {
        condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body });
      } else {
        if (scriptType === "inlineScript") {
          condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body, language });
          this.$set(this.flowConditionForm, "resource", "");
        } else {
          this.$set(this.flowConditionForm, "body", "");
          condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { resource, language });
        }
      }
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, { conditionExpression: condition });
    }
  },
  beforeDestroy() {
    this.bpmnElement = null;
    this.bpmnElementSource = null;
    this.bpmnElementSourceRef = null;
  }
};
</script>
