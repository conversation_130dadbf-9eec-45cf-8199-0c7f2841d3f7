<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
<style>
/* 表头样式 */
.el-table thead th {
  background-color: rgba(130, 92, 228, .07) !important;
  font-size: 14px;
  font-weight: bold;
  color: #202225;
  line-height: 42px;
  text-align: left !important;
  border-bottom: 1px solid #ffffff !important;
  /* 底部分割线 */
  border-right: 1px solid #ffffff !important;
  /* 列之间分割线 */
}

/* 表格主体样式 */
.el-table tbody td {
  background-color: #ffffff;
  font-size: 14px;
  font-weight: normal;
  color: #333333;
  line-height: 42px;
}

.custom-popover {
  max-width: 800px;
}

.el-select {
  width: 100%;
}
</style>
