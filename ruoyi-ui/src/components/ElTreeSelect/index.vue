<template>
  <el-select ref="elSelect" :value="valueTitle" :clearable="clearable" @clear="clearHandle" style="width: 100%;"
    @visible-change="onVisibleChange">
    <el-option :value="valueTitle" :label="valueTitle">
      <el-tree id="tree-option" ref="selectTree" :accordion="accordion" :data="options" :props="props"
        :node-key="props.value" :default-expanded-keys="defaultExpandedKey" :expand-on-click-node="false"
        @node-click="handleNodeClick" @expand-change="onExpandChange">
        <!-- 自定义节点图标，点击图标仅展开/收起 -->
        <template #default-expand-icon="{ node, store }">
          <i class="el-tree-node__expand-icon el-icon-arrow-right" :class="{ 'is-expanded': node.expanded }"
            @click.stop="toggleNode(node)"></i>
        </template>
        <template #default-collapse-icon="{ node, store }">
          <i class="el-tree-node__expand-icon el-icon-arrow-down" @click.stop="toggleNode(node)"></i>
        </template>
      </el-tree>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: "el-tree-select",
  props: {
    props: {
      type: Object,
      default: () => ({
        value: 'id',
        label: 'title',
        children: 'children'
      })
    },
    options: {
      type: Array,
      default: () => []
    },
    value: {
      type: [String, Number, null],
      default: null
    },
    clearable: {
      type: Boolean,
      default: true
    },
    accordion: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      valueId: this.value,
      valueTitle: '',
      defaultExpandedKey: [],
      dropdownVisible: false
    }
  },
  mounted() {
    this.initHandle()
  },
  methods: {
    initHandle() {
      if (this.valueId != null) {
        const node = this.$refs.selectTree.getNode(this.valueId)
        if (node) {
          this.valueTitle = node.data[this.props.label]
          this.$refs.selectTree.setCurrentKey(this.valueId)
          this.defaultExpandedKey = [this.valueId]
        }
      }
      // 隐藏滚动条
      this.$nextTick(() => {
        const wrap = document.querySelector('.el-select-dropdown__wrap')
        if (wrap) {
          wrap.style.cssText = 'margin:0;max-height:none;overflow:hidden;'
          document.querySelectorAll('.el-scrollbar__bar').forEach(bar => bar.style.width = '0')
        }
      })
    },
    // 切换下拉可见状态
    onVisibleChange(val) {
      this.dropdownVisible = val
      if (val) {
        this.initHandle()
      }
    },
    // 点击文字选中并关闭
    handleNodeClick(data) {
      this.valueTitle = data[this.props.label]
      this.valueId = data[this.props.value]
      this.$emit('input', this.valueId)
      this.$emit('change')
      this.defaultExpandedKey = []
      // 关闭下拉
      this.$refs.elSelect.toggleMenu()
    },
    // 点击清空
    clearHandle() {
      this.valueTitle = ''
      this.valueId = null
      this.defaultExpandedKey = []
      this.clearSelected()
      this.$emit('input', null)
      this.$emit('change')
    },
    clearSelected() {
      document.querySelectorAll('#tree-option .el-tree-node.is-current')
        .forEach(el => el.classList.remove('is-current'))
    },
    // 手动展开/收起某个节点
    toggleNode(node) {
      node.expanded = !node.expanded
    },
    // keep defaultExpandedKey in sync
    onExpandChange(node, expandedKeys) {
      this.defaultExpandedKey = [...expandedKeys]
    }
  },
  watch: {
    value(newVal) {
      this.valueId = newVal
      this.initHandle()
    },
    options: {
      handler(val, Oval) {
        console.log(val, 'val')
        this.$nextTick(() => {
          this.initHandle()

        })
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 274px;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}

.el-tree-node__label {
  font-weight: normal;
}

/* 当前选中高亮 */
.el-tree>>>.is-current .el-tree-node__label {
  color: #825ce4;
  font-weight: 700;
}

.el-tree>>>.is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

/* 自定义展开/收起图标样式 */
.el-tree-node__expand-icon {
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;
}

.el-tree-node__expand-icon.is-expanded {
  transform: rotate(90deg);
}
</style>
