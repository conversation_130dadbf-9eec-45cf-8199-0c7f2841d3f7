<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "LoginForm",
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "admin",
        password: "admin123",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: false,
      // 注册开关
      register: false,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.data.img;
          this.loginForm.uuid = res.data.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || "/" }).catch(() => { });
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    }
  }
};
</script>

<template>
  <div class="login-form-container">
    <span>新华资产运营管理系统</span>
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" label-width="0" @keydown.native.enter="handleLogin">
      <el-form-item prop="username">
        <div class="login-form-item">
          <div class="login-form-item-prefix">
            <img src="@/assets/images/account.png" alt="账号图标" />
          </div>
          <div class="login-form-input">
            <el-input v-model="loginForm.username" placeholder="请输入账号"></el-input>
          </div>
        </div>
      </el-form-item>

      <el-form-item prop="password">
        <div class="login-form-item">
          <div class="login-form-item-prefix">
            <img src="@/assets/images/password.png" alt="密码图标" />
          </div>
          <div class="login-form-input">
            <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password></el-input>
          </div>
        </div>
      </el-form-item>

      <div class="form-action">
        <div class="form-action-remember">
          <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
        </div>
        <div class="form-action-recommend">
          <i class="el-icon-warning"></i>
          推荐使用谷歌浏览器
        </div>
      </div>

      <el-form-item>
        <div class="login-form-button">
          <el-button :loading="loading" type="primary" size="medium" class="is-block" @click="handleLogin"
            style="width: 100%;">
            登录
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.login-form-container {
  // flex: 1;
  width: 648px;
  height: 586px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 93px 72px;
  // background: linear-gradient(0deg, #FDE4EA 0%, #FFEBEC 99%);
  background: linear-gradient(0deg, #D4EAFF 0%, #E1EFFD 99%);
  border-radius: 16px;

  &>span {
    font-weight: bold;
    font-size: 32px;
    color: #4e5969;
    text-align: center;
  }

  .el-form {
    width: 100%;
    margin-top: 70px;

    .login-form-item {
      width: 100%;
      height: 56.3px;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 18px 18px 18px 0;
      border-radius: 4px;
      border: 1px solid #E7E7E7;
      background: #FFFFFF;
      box-shadow: 0px 2px 8px 0px rgba(7, 19, 43, 0.06);

      .login-form-item-prefix {
        width: 64px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #bbb;
      }

      .login-form-input {
        flex: 1;



        .el-input {

          /* 深度穿透到内部 el-input__inner */
          ::v-deep .el-input__inner {
            font-size: 18px;
            background: #fff;
            border: none;
          }
        }

      }
    }

    .form-action {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
    }

    .login-form-button {
      width: 100%;
      margin-top: 51px;

      button {
        height: 56.3px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 20px;
        color: #FCFEFF;
        text-shadow: 0px 1px 2px rgba(0, 26, 70, 0.55);
      }
    }
  }
}
</style>
