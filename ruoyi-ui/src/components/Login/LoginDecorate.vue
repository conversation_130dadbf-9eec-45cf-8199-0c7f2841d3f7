<script>
export default {
  name: 'LoginDecorate'
}
</script>

<template>
  <div class="login-decorate-container">
    <div class="header-contianer">
      <span />
    </div>
    <!-- <div class="content-container">
      <img src="@/assets/svgs/login-decorate.svg" />
      <span>请输入您的账号密码登录使用！</span>
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
.login-decorate-container {
  // flex: 1;
  width: 976px;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 1;

  .header-contianer {
    display: flex;
    align-items: center;
    column-gap: 16px;

    span {
      &:first-child {
        width: 159px;
        height: 39px;
        background: url('../../assets/images/login-logo.png') no-repeat;
        background-size: 100% 100%;
      }

      &:last-child {
        font-weight: bold;
        font-size: 24px;
        line-height: 1;
        color: #825ce4;
      }
    }
  }

  .content-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;

    img {
      width: 80%;
      margin-bottom: 16px;
    }

    span {
      font-size: 14px;
      line-height: 1;
      color: #fff;
    }
  }
}
</style>
