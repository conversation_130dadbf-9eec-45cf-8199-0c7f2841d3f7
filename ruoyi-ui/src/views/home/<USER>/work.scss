.workbench-work {
  width: 100%;
  height: 100%;
  padding: 30px 20px;
  background: url("../assets/card-decorate.png") top right no-repeat;
  background-size: 214px 76px;
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;

  &.is-normal {
    .workbench-tab {
      margin-left: auto;
    }

    .el-select {
      width: auto;
    }
  }

  // &.is-small{

  // }

  // &.is-middle{

  // }

  &-header {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(#bfbfbf, 0.6);
  }

  &__title {
    font-family: Source Han <PERSON>N <PERSON>, serif;
    font-weight: bold;
    font-size: 18px;
    color: #202225;
  }

  .workbench-tab {
    margin-left: 30px;
  }

  .workbench-tab__item.is-active::before {
    bottom: -12px;
  }

  &-search {
    display: flex;
    align-items: center;
    margin-bottom: 13px;
    overflow-x: auto;
  }

  .el-select {
    width: 82px;
  }
  .el-range-editor {
    width: 220px;
  }

  &-main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  &-item {
    margin-left: auto;

    &__label {
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 14px;
      color: #4b5566;
    }

    .el-select {
      margin-right: 16px;
    }
  }

  .custom-table {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    &.is-draggable {
      overflow: hidden;
      .el-table__body-wrapper {
        overflow: hidden;
      }
    }

    thead tr th {
      background: rgba(#825ce4, 0.07);
      font-family: Source Han Sans CN Bold, serif;
      font-weight: bold;
      font-size: 14px;
      color: #202225;

      & > .cell {
        font-family: Source Han Sans CN Bold, serif;
        height: 34px;
        line-height: 34px;
      }
    }

    tbody tr td .cell {
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }

    .el-table__expanded-cell {
      padding: 0;
    }

    .el-table__body-wrapper {
      max-height: calc(100% - 80px);
      overflow: hidden auto;
    }
    .el-table__body-wrapper,
    .el-table__empty-text {
      height: 100%;
    }

    .el-radio__input.is-checked .el-radio__inner {
      border-color: #825ce4;
      background-color: #825ce4;
    }
    .el-radio__inner:hover {
      border-color: #825ce4;
    }
    .el-radio__inner::after {
      width: 8px;
      height: 8px;
    }
    .el-radio .el-radio__label,
    .el-radio__input.is-checked + .el-radio__label {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
  }

  .el-table .ascending .sort-caret.ascending {
    border-bottom-color: #000;
  }
  .el-table .sort-caret.ascending {
    border-bottom-color: rgba(#000, 0.4);
  }
  .el-table .sort-caret.descending {
    border-top-color: rgba(#000, 0.4);
  }
  .el-table .descending .sort-caret.descending {
    border-top-color: #000;
  }

  .child-table {
    width: 100%;
    height: 100%;
    margin-left: 67px;

    thead th th,
    tbody tr td {
      background: #f5f6fc;
    }

    thead tr th .cell {
      font-family: Source Han Sans CN, serif;
      font-weight: bold;
      font-size: 14px;
      color: #202225;
    }

    tbody tr td .cell {
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }

    tbody tr td:first-child {
      border-left: 3px solid transparent;
    }

    tbody tr.is-active,
    tbody tr.current-row {
      background: rgba(#825ce4, 0.07) !important;
      td {
        .cell {
          color: #3d2282 !important;
          background-color: transparent;
          .el-radio .el-radio__label {
            color: #3d2282;
          }
        }
        color: #2727b2 !important;
        background: rgba(#825ce4, 0.15) !important;

        &:first-child {
          border-left: 3px solid #825ce4;
        }
      }
    }
  }
  .el-table .el-table__cell {
    padding: 6px 0;
  }

  .el-table .el-table__row.is-error td .cell {
    .el-radio .el-radio__label {
      color: #e3241b;
    }
    color: #e3241b;
  }

  .el-table .el-table__row.is-warn td .cell {
    color: #ea6c10;
    .el-radio .el-radio__label {
      color: #ea6c10;
    }
  }

  .el-table .el-table__expanded-cell {
    padding: 0;
  }

  .el-table__expand-icon {
    transform: rotate(0deg);
  }

  .el-table__expand-icon > .el-icon {
    width: 14px;
    height: 14px;
    margin-top: -7px;
  }

  .el-table__expand-icon .el-icon-arrow-right::before {
    content: "";
    width: 14px;
    height: 14px;
    display: inline-block;
    background-size: cover;
    background: url("../assets/table-expand.png") no-repeat;
  }

  .el-table__expand-icon.el-table__expand-icon--expanded .el-icon-arrow-right {
    color: #acb4ff;
  }
  .el-table__expand-icon.el-table__expand-icon--expanded
    .el-icon-arrow-right::before {
    content: "";
    background: url("../assets/table-open.png") no-repeat;
  }

  .el-radio-group {
    display: flex;
  }
  .el-radio {
    margin-right: 8px;
  }
  .el-radio .el-radio__label {
    padding-left: 4px;
  }

  .el-radio-button__inner:hover {
    color: #3d2282;
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: #3d2282;
    background-color: #d9cef7;
    border-color: #825ce4;
    box-shadow: -1px 0 0 0 #825ce4;
  }

  &::before,
  &::after {
    content: "";
    width: 29px;
    height: 29px;
    background: url("../assets/card-horn-decorate.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    bottom: 0;
  }
  &::after {
    left: initial;
    right: 0;
    transform: rotateZ(-90deg);
  }
}

// .scale-select-panel {
//   position: absolute !important;
//   top: 28px !important;
//   left: 0 !important;
//   width: 100%;
// }

// .scale-work-picker-panel {
//   right: 0 !important;
//   left: unset !important;
// }
