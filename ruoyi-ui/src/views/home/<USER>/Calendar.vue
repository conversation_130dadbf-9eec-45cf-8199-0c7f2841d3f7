<template>
  <section class="workbench-calendar" :class="[`is-${size}`]">
    <header class="workbench-calendar-header">
      <!-- <ul class="workbench-calendar-tab">
        <li
          v-for="item in calendarType"
          :key="item.value"
          class="workbench-calendar-tab__item"
          :class="[calendarActiveType === item.value && 'is-active']"
          @click="onChangecalendarActiveType(item.value)">
          {{ item.label }}
        </li>
      </ul> -->

      <ul class="workbench-calendar-tags">
        <li
          v-for="item in shareType"
          :key="item.value"
          class="workbench-calendar-tags__item"
          :class="[`is-${item.value}`]">
          {{ item.label }}
        </li>
      </ul>

      <!-- <div class="workbench-calendar__line"></div> -->

      <button class="workbench__button" style="margin-left: auto;" @click="jumpWork">值班管理</button>
    </header>

    <section class="workbench-calendar-info">
      <div class="workbench-calendar__date">{{ displayMonth }}</div>
      <div class="workbench-calendar__day" @click="changeDate()">今天</div>
      <div class="workbench-calendar__prev" @click="prevMonth">
        <span class="el-icon-arrow-left"></span>
      </div>
      <div class="workbench-calendar__next" @click="nextMonth">
        <span class="el-icon-arrow-right"></span>
      </div>
      <div class="workbench-calendar__line" style="height: 14px"></div>
      <el-button
        class="workbench-calendar__event"
        type="text"
        size="small"
        icon="el-icon-plus"
        @click="handleCreateEventOpen">
        添加日程
      </el-button>
    </section>

    <main ref="main" class="workbench-calendar-main">
      <div class="workbench-calendar__row workbench-calendar-row-week">
        <span v-for="item in weekDays" :key="item" class="workbench-calendar__col is-week">
          {{ item }}
        </span>
      </div>
      <div v-for="(item, inx) in calendar" :key="`calendar-row-${inx}`" class="workbench-calendar__row">
        <div v-for="day in item" :key="`calendar-col-${day.day}`" style="flex: 1; height: 100%; overflow: hidden">
          <span
            class="workbench-calendar__col"
            :class="{
              'is-reset': day.isRestDay && !getAdjustRest(day.fullDate),
              'is-prev': day.isPreviousMonth,
              'is-next': day.isNextMonth,
              'is-today': currentDay === day.fullDate,
            }"
            @click="changeDate(day)">
            <i
              v-if="canDisplayHoliday(day.fullDate, internalHolidayList)"
              class="workbench-calendar__close"
              :class="getHolidayClass(day.fullDate, internalHolidayList)">
              休
            </i>
            <span class="workbench-calendar-day">{{ day.day }}</span>
            <i class="workbench-calendar-dots">
              <i
                v-for="item in getDots(day.fullDate, holidayList, eventList)"
                :key="`workbench-calenday-dot-${day.fullDate}-${item}`"
                :class="item" />
            </i>
          </span>
          <div v-if="currentDayEvents.length && mainHeight > 540" class="workbench-calendar-events">
            <el-tooltip v-for="item in currentDayEvents" :key="item.title" :content="item.title" placement="top">
              <span>{{ item.title }}</span>
            </el-tooltip>
          </div>
        </div>
      </div>
    </main>

    <section class="workbench-calendar-footer">
      <div v-for="item in footerList" :key="item.value" class="workbench-calendar-footer__item">
        <span class="workbench-calendar-footer__dot" :class="[`is-${item.value}`]"></span>
        <span class="workbench-calendar-footer__label">{{ item.label }}</span>
      </div>
    </section>

    <el-dialog
      :visible="eventState.visible"
      :append-to-body="true"
      title="添加日程"
      width="30%"
      @close="handleCreateEventClose">
      <el-form ref="eventForm" :model="eventState.formData" :rules="eventFormRules" label-width="80px">
        <el-form-item prop="title" :label="eventLabelMap.title">
          <el-input
            v-model="eventState.formData.title"
            :maxlength="15"
            :show-word-limit="true"
            :placeholder="eventLabelMap.title" />
        </el-form-item>
        <el-form-item prop="content" :label="eventLabelMap.content">
          <el-input
            v-model="eventState.formData.content"
            type="textarea"
            :rows="3"
            :maxlength="300"
            :show-word-limit="true"
            :placeholder="eventLabelMap.content" />
        </el-form-item>
        <el-form-item prop="date" :label="eventLabelMap.date">
          <el-date-picker
            v-model="eventState.formData.date"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd HH:mm"
            style="width: 100%"
            popper-class="scale-picker-panel"
            :append-to-body="false"
            :default-time="['08:30:00', '17:30:00']" />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="handleCreateEventClose">取消</el-button>
        <el-button :loading="eventState.loading" type="primary" @click="handleCreateEvent()">确定</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
import calendarMixin from '@/views/home/<USER>/calendar-mixin';
import calendarCreateEventMixin from '../minxins/calendar-create-event.mixin';
import { hasPermission2D } from '@/utils';

export default {
  name: 'Calendar',
  mixins: [calendarMixin, calendarCreateEventMixin],
  created() {
    this.getData();
  },
  mounted() {
    // 判断值班管理权限
    this.schedulingIsShow = hasPermission2D(this.$store, 'operateMonitor', 'scheduling');
    const resizeOb = new ResizeObserver(this.watchResize.bind(this));
    resizeOb.observe(this.$refs.main);
    this.resizeOb = resizeOb;

    // watchEffect((onInvalidate) => {
    //   if (!this.$refs.main) return;

    //   const observer = new ResizeObserver((entries) => {
    //     // entries[0].contentRect.height;
    //     // console.log('🚀 ~ entries[0].contentRect.height:', entries[0].contentRect.height);
    //     this.mainHeight = entries[0].contentRect.height
    //   });

    //   observer.observe(this.$refs.main);

    //   // 自动清理
    //   onInvalidate(() => {
    //     observer.disconnect();
    //   });
    // });
  },
  beforeDestroy() {
    this.destoryOb();
  },
  data: () => {
    return {
      schedulingIsShow: false,
      size: 'normal',
      mainHeight: 0,
    };
  },
  methods: {
    destoryOb() {
      if (this.resizeOb) {
        this.resizeOb.disconnect();
      }
    },
    watchResize([entire]) {
      if (entire.contentRect.height >= 165) {
        this.size = 'normal';
      } else if (entire.contentRect.height >= 115) {
        this.size = 'middle';
      } else {
        this.size = 'small';
      }
    },
  },
};
</script>

<style lang="scss" src="../styles/calendar.scss"></style>
