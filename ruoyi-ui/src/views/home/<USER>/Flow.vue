<template>
  <NotifyCard class="workbench-flow" title="流程中心">
    <template slot="opera">
      <div class="workbench-notify-card__more" @click="toFlows(activeTab)">
        查看更多
        <i class="el-icon-arrow-right"></i>
      </div>
    </template>
    <template slot="headerContent">
      <ul class="workbench-tab">
        <li v-for="item in getTabList" :key="item.value" class="workbench-tab__item" :class="{
          'is-active': activeTab === item.value,
        }" @click="handleSwitchTab(item.value)">
          {{ item.label }}
          <span v-if="item.count > 0">{{
            item.count > 99 ? 99 : item.count
            }}</span>
        </li>
      </ul>
    </template>
    <Empty class="workbench-timeline" :class="{
      'is-draggable': isDraggable,
    }" :empty="!flowList.length">
      <div v-for="item in flowList" :key="item.procInsId" class="workbench-timeline-item">
        <div class="workbench-timeline-item__dot is-success"></div>
        <div class="workbench-timeline-item-content">
          <div class="workbench-timeline-item-info-content">
            <!-- <div :data-type="item.type" class="workbench-timeline-item__tag">
              {{ item.procDefName }}
            </div> -->
            <dict-tag :options="dict.type.wf_process_status" :value="item.processStatus" />
            <div :title="item.processName" class="workbench-timeline-item__name">
              {{ item.processName || item.procDefName }}
            </div>
            <div class="workbench-timeline-item__detail" @click="toFlowDetail(item, activeTab)">
              {{ activeTab === 'todoProcessList' ? '办理' : '详情' }}
            </div>
          </div>
        </div>
      </div>
    </Empty>
  </NotifyCard>
</template>

<script>
import tabMixin from "@/views/home/<USER>/tab-mixin";
import NotifyCard from "@/views/home/<USER>/NotifyCard.vue";
import Empty from "./Empty.vue";
import { listOwnProcess, listTodoProcess } from "@/api/workflow/process.js";

const tabList = [
  {
    label: "我的待办",
    value: "todoProcessList",
  },
  {
    label: "我的发起",
    value: "processList",
  },
];
export default {
  name: "Flow",
  mixins: [tabMixin],
  dicts: ['wf_process_status'],
  components: {
    NotifyCard,
    Empty,
  },
  inject: ["getFlowList", "toFlows", "toFlowDetail"],
  props: {
    isDraggable: {
      type: Boolean,
    },
  },
  data: () => {
    return {
      activeTab: tabList[0].value,
      flowMapList: {
        processList: [],
        todoProcessList: [],
      },
    };
  },
  computed: {
    getTabList() {
      return tabList.map((item) => {
        return {
          ...item,
          count: this.flowMapList[item.value].length,
        };
      });
    },
    flowList() {
      return this.flowMapList[this.activeTab];
    },
  },
  created() {
    this.initFlowList();
  },
  activated() {
    this.initFlowList()
  },
  methods: {
    async initFlowList() {
      this.flowMapList.processList = (
        await listOwnProcess({
          pageNum: 1,
          pageSize: 10,
        })
      ).rows;

      this.flowMapList.todoProcessList = (
        await listTodoProcess({
          pageNum: 1,
          pageSize: 10,
        })
      ).rows;
    },
    getTargetFlowList(type) {
      return this.getFlowList().filter((item) => item.type === type);
    },
  },
};
</script>
