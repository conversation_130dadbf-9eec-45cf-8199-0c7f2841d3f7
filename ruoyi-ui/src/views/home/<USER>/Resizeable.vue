<template>
  <div
    class='resizeable'
    :class='{
      "is-resize": isResizeable,
    }'
  >
    <slot></slot>
    <div v-if='isResizeable' class='resizeable-mask' data-drag='handle'></div>
    <div v-if='isResizeable' class='resizeable-remove' @click='handleRemove'>
      <span class='el-icon-close' />
    </div>
  </div>
</template>

<script >
  export default {
    name: 'Resizeable',
    props: {
      isResizeable: {
        type: Boolean,
        default: false
      }
    },
    emits: ['on-remove'],
    methods: {
      handleRemove() {
        this.$emit('on-remove')
      },
    }
  }
</script >

<style lang='scss'>
  .resizeable{
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;

    &-mask{
      content: "";
      display: block;
      width: 95%;
      height: 95%;
      position: absolute;
      top: 2.5%;
      left: 2.5%;
      z-index: 99;
      user-select: none;
    }

    &-handle{
      position: absolute;
      border-radius: 3px;
      background-color: #7756FF;
      z-index: 100;

      &.is-vertical{
        left: calc(50% - 25%);
        bottom: 0;
        width: 50%;
        height: 3px;
        cursor: ns-resize;
      }

      &.is-horizontal{
        top: calc(50% - 25%);
        right: 0;
        width: 3px;
        height: 50%;
        cursor: ew-resize;
      }

      &.is-full{
        right: 0;
        bottom: 0;
        width: 30px;
        height: 30px;
        background-color: transparent;
        border-style: solid;
        border-right-color: #7756FF;
        border-bottom-color: #7756FF;
        border-width: 1px;
        border-top-width: 0;
        border-left-width: 0;
        border-bottom-right-radius: 10px;
        cursor: nwse-resize;
      }
    }

    &-remove{
      position: absolute;
      font-weight: bold;
      font-size: 24px;
      color: #a193ff;
      top: 4px;
      right: 4px;
      z-index: 100;
      cursor: pointer;
      .el-icon-close {
        font-weight: bold;
      }
    }
  }
</style >
