.workbench {
  width: 100%;
  height: 100%;
  // min-width: 1920px;
  // max-width: 1920px;
  // min-height: 811px;
  // max-height: 811px;
  // margin: 0 auto;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;

  &.is-draggable {
    &::before {
      content: '';
      display: block;
      position: absolute;
      z-index: 0;
      width: 100%;
      height: 100%;
      background-image: linear-gradient(to right, rgba(#7756ff, 0.3) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(#7756ff, 0.3) 1px, transparent 1px);
      background-size: 8px 8px; /* 调整格子大小 */
    }
  }

  .vue-grid-item.vue-grid-placeholder {
    background: #7756ff;
    border-radius: 8px;
  }

  &-wrapper {
    width: 100%;
    height: 100%;
    background-color: #e5e5eb;
    overflow: auto;

    &.is-draggable {
      // overflow: hidden;
      * {
        user-select: none;
      }
    }
  }

  .vue-grid-layout {
    width: 100%;
    height: 100%;

    // max-width: 100%;
    // min-height: 811px;
    // max-height: 811px;
  }

  // .el-dropdown-menu {
  //   z-index: 9999 !important;
  // }

  &__button {
    font-family: Source Han Sans CN, serif;
    font-weight: 400;
    font-size: 14px;
    color: #3d2282;
    width: 92px;
    height: 32px;
    border: 1px solid #825ce4;
    background-color: #d9cef7;
    cursor: pointer;
    border-radius: 3px;
    transition: opacity 300ms;

    & + button {
      margin-left: 16px;
    }

    &.is-outline {
      border-color: rgba(#ab88da, 0.4);
      background-color: transparent;
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 14px;
      color: rgba(#202225, 0.8);
    }

    &:hover {
      opacity: 0.9;
    }
  }
}

.workbench-tab {
  display: flex;
  align-items: center;
  column-gap: 20px;

  &__item {
    font-family: Source Han Sans CN, serif;
    font-weight: 400;
    font-size: 14px;
    cursor: pointer;
    color: rgba(#202225, 0.7);
    position: relative;
    & > span {
      width: 16px;
      height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background-color: #ff3030;
      font-weight: 400;
      font-size: 10px;
      line-height: 1;
      color: #fff;
      position: absolute;
      top: -7px;
      right: -13px;
    }

    &.is-active {
      font-family: Source Han Sans CN Bold, serif;
      font-weight: bold;
      color: #333333;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        height: 3px;
        background: #825ce4;
        border-radius: 1px;
      }
    }
  }
}

.workbench-timeline {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden auto;
  padding-right: 5px;
  display: flex;
  flex-direction: column;
  padding-bottom: 6px;

  &.is-draggable {
    overflow: hidden;
  }

  &-item {
    width: 100%;
    position: relative;
    display: flex;
    align-items: flex-start;
    padding-bottom: 10px;

    &:last-child {
      padding-bottom: 0;
    }

    &:last-child::after {
      display: none;
    }

    &::after {
      position: absolute;
      content: '';
      display: block;
      top: 0;
      left: 6px;
      border-left: 1px solid #eee;
      width: 6px;
      height: 100%;
      z-index: 0;
    }

    &__dot {
      flex: 0 0 9px;
      position: relative;
      display: inline-block;
      width: 9px;
      height: 9px;
      border-radius: 50%;
      border: 2px solid #cbcbcb;
      z-index: 2;
      background: #fff;
      margin-left: 2px;
    }

    &-content {
      flex: 1;
      margin-left: 8px;
      width: calc(100% - 8px);
      overflow: hidden;
    }

    &-header {
      display: flex;
      align-items: center;
      margin-top: -2px;
      margin-bottom: 6px;
    }

    &__time {
      margin-left: 8px;
      margin-right: 15px;
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 14px;
      color: #4b5566;
    }

    &__read {
      display: inline-block;
      color: #d0d4d9;
      background-color: #f6f8fa;
      border: 1px solid #f2f3f4;
      padding: 2px 4px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: bold;

      &.is-read {
        color: #2173f5;
        background-color: #c7ddff;
        border-color: #70a7ff;
      }
    }

    &-info {
      width: 100%;
      box-sizing: border-box;

      border-radius: 4px;
      display: flex;
      align-items: center;
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      &-content {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 10px 8px;
        background-image: linear-gradient(to right, rgba(#825ce4, 0.135), transparent);
      }
    }

    &__order {
      width: 32px;
      text-align: center;
      padding: 10px 8px;
      border-radius: 4px;
      background: rgba(#a6b0e1, 0.15);
      margin-right: 6px;
    }

    &__tag {
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      padding: 3px 8px;
      border-radius: 0 4px 0 4px;
      margin-right: 8px;
      background-image: url('../assets/flow/product-flow-icon.png');
      background-size: cover;
      background-repeat: no-repeat;

      &[data-type='内报报送'],
      &[data-type='外报报送'] {
        background-image: url('../assets/flow/internal-send-icon.png');
      }

      &[data-type='费用支付'] {
        background-image: url('../assets/flow/pay-icon.png');
      }

      &[data-type='用印流程'] {
        background-image: url('../assets/flow/post-seal.png');
      }
    }

    &__name {
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.5;
      color: #303741;
      width: 260px;
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 隐藏超出内容 */
      text-overflow: ellipsis; /* 使用省略号 */
    }
    &__status {
      flex: none;
      width: 20px;
      height: 20px;
      margin-right: 8px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      &.warning {
        background-image: url('../assets/monitor/icon-warning.png');
      }
      &.fail {
        background-image: url('../assets/monitor/icon-fail.png');
      }
    }
    &__detail {
      flex: none;
      margin-left: auto;
      cursor: pointer;
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 12px;
      color: #3d2282;
    }
  }
  &-monitor-item {
    .workbench-timeline-item__tag {
      &.fail {
        background-image: url('../assets/monitor/tag_background_fail.png');
      }
      &.warning {
        background-image: url('../assets/monitor/tag_background_warning.png');
      }
    }
    .workbench-timeline-item__name {
      width: auto;
      max-width: 100%;
      margin-right: 8px;
    }
  }
}
