<template>
  <div class='workbench-operate'>
    <div v-show='isMoveing' class='workbench-operate-mask'/>
    <div
      v-show='!isDraggable'
      class='workbench-operate-button'
      :style='positionStyle'
      @mousedown='handleMousedown'
    >
      <span class='el-icon-setting'></span>
    </div>
    <section
      v-show='isDraggable'
      class='workbench-operate-drawer'
      :class='{"is-show": isDraggable,"is-collapsed": isCollapsible}'
    >
      <header v-show='!isCollapsible' class='workbench-operate-drawer-header'>
        <h3 class='workbench-operate-drawer__title'>配置页面</h3>
        <span class='el-icon-close' @click='handleCloseDrawer'></span>
      </header>
      <main v-show='!isCollapsible' class='workbench-operate-drawer-main'>
        <section class='workbench-operate-section'>
          <h4 class='workbench-operate__title'>主题</h4>
          <ul class='workbench-operate-themes'>
            <li
              class='workbench-operate-themes-item'
              @click='handleChangeTheme("default")'
              :class='{
                "is-active": theme === "default"
              }'
            >
              <span class='workbench-operate-themes__layout'></span>
            </li>
            <li
              class='workbench-operate-themes-item is-blue'
              @click='handleChangeTheme("blue")'
              :class='{
                "is-active": theme === "blue"
              }'
            >
              <span class='workbench-operate-themes__layout is-blue'></span>
            </li>
          </ul>
        </section>
        <section class='workbench-operate-section'>
          <h4 class='workbench-operate__title'>组件列表</h4>
          <el-input v-model='searchValue' size='small' prefix-icon="el-icon-search" />
          <el-collapse v-model='expandNames'>
            <el-collapse-item
              v-for='item in filtedComponentList'
              :key='item.label'
              :title='item.label'
              :disabled='item.dispabled'
              :name='item.label'
              class='workbench-operate-panel'
            >
              <div
                v-for='info in item.children'
                :key='info.label'
                class='workbench-operate-panel-item'
                draggable="true"
                unselectable="on"
                @drag="drag(info.data)"
                @dragend="dragend"
              >
                <span class='workbench-operate-panel__name'>{{info.label}}</span>
                <span class='workbench-operate-panel__image'>
                <img :src='componentCovers[info.data.key]' :alt='info.label'/>
              </span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </section>
      </main>
      <footer v-show='!isCollapsible' class='workbench-operate-drawer-footer'>
        <button class='workbench__button' @click='handleSave'>保存布局</button>
        <el-button size='small' @click='handleReset'>重置布局</el-button>
        <el-button size='small' @click='handleCloseDrawer'>关闭</el-button>
      </footer>
      <div class='workbench-operate-drawer__collapse' :class='{"is-collapsed": isCollapsible}' @click='handleToggleCollapse' />
    </section>
  </div>
</template>

<script >
  import {componentList,componentCovers} from "../constant";
  export default {
    name: "Operate",
    props:{
      isDraggable: {
        type: Boolean,
        default: false
      },
      theme: {
        type: String,
        default: 'default'
      }
    },
    data: () => {
      return {
        position: {
          x: 0,
          y: 200
        },
        isMoveing: false,
        isCollapsible: true,
        searchValue: '',
        expandNames: [],
        componentList,
        componentCovers
      }
    },
    emits: ['open','close','reset','add','save','cus-drag','cus-dragend'],
    computed: {
      positionStyle(){
        return {
          top: this.position.y + 'px',
          left: this.position.x + 'px',
        }
      },
      filtedComponentList({searchValue}){
        return componentList.map(item => {
          const children = item.children.filter(item => item.label.match(searchValue))
          return {
            ...item,
            dispabled: !children.length,
            children
          }
        })
      }
    },
    methods: {
      handleChangeTheme(theme){
        this.$emit('change-theme',theme)
      },
      handleMousedown(ev) {
        const self = this
        const startTime = Date.now()
        let [startX,startY] = [ev.pageX,ev.pageY]
        this.isMoveing = true
        document.onmousemove = (e) => {
          const [gapX,gapY] = [e.pageX - startX,e.pageY - startY]
          ;[startX,startY] = [e.pageX,e.pageY]
          const [finalX,finalY] = [self.position.x + gapX,self.position.y + gapY]
          this.position = {
            x: finalX <= 0 ? 0 : finalX,
            y: finalY <= 0 ? 0 : finalY
          }
        }
        document.onmouseup = () => {
          if (Date.now() - startTime <= 500){
            self.$emit('open',true)
          }
          this.isMoveing = false
          document.onmousemove = document.onmouseup = null
        }
      },
      handleToggleCollapse(){
        console.log(this.isCollapsible)
        this.isCollapsible = !this.isCollapsible
      },
      handleCloseDrawer(){
        this.$emit('close')
      },
      handleReset(){
        this.$emit('reset')
      },
      handleSave(){
        this.$emit('save')
      },
      drag(data){
        this.$emit('cus-drag',data)
        this.isCollapsible = true
      },
      dragend(){
        this.$emit('cus-dragend')
        this.isCollapsible = false
      }
    },
    watch:{
      searchValue(value){
        if (value){
          this.expandNames = this.filtedComponentList.filter(item => !item.dispabled).map(item => item.label)
          console.log(this.filtedComponentList.filter(item => !item.dispabled).map(item => item.label))
        } else {
          this.expandNames = []
        }
      }
    }
  }
</script >

<style lang='scss'>
  .workbench-operate{
    &-section{
      margin-bottom: 16px;
    }

    &__title{
      font-size: 18px;
      margin-bottom: 16px;
    }

    &-themes{
      display: flex;
      column-gap: 16px;

      &-item{
        flex: 1;
        width: 100%;
        height: 100%;
        cursor: pointer;
        padding: 6px;
        border-radius: 6px;

        &.is-active{
          border: 2px solid #7756FF;
        }

        &.is-blue.is-active{
          border-color: #165DFF;
        }
      }

      &__layout{
        width: 150px;
        height: 56px;
        display: inline-block;
        background-image: url("../assets/layout/purple-layout.png");
        background-size: 160px 76px;

        &.is-blue{
          background-image: url("../assets/layout/blue-layout.png");
        }
      }
    }

    &-mask{
      position: absolute;
      width: 100%;
      height: 100%;
      min-height: 811px;
      max-height: 811px;
      top: 0;
      left: 0;
      z-index: 98;
    }

    &-button{
      position: absolute;
      top: 200px;
      right: 6px;
      width: 30px;
      height: 30px;
      color: #7756FF;
      background: #fff;
      z-index: 99;
      text-align: center;
      line-height: 30px;
      cursor: pointer;
      border-radius: 50%;
    }

    &-drawer{
      position: absolute;
      top: 16px;
      left: 0;
      height: calc(100% - 32px);
      background-color: #fff;
      width: 400px;
      max-height: calc(811px - 32px);
      z-index: 99;
      border-radius: 8px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      row-gap: 16px;
      padding: 16px;
      transition: width 500ms;

      &.is-collapsed{
        padding: 0;
        width: 7px;
        overflow: hidden;
      }

      &-header{
        flex: 0 0 31px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
      }

      &__title{
        font-family: Source Han Sans CN,serif;
        font-size: 18px;
        color: #202225;
      }

      &-main{
        flex: 1;
        overflow-y: auto;
        padding-right: 16px;
      }

      &-footer{
        border-top: 1px solid #eee;
        flex: 0 0 60px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      &__collapse{
        position: absolute;
        top: calc(50% - 30px);
        right: 1px;
        width: 6px;
        height: 60px;
        border-radius: 6px;
        background-color: rgba(#7756FF,.8) ;
        z-index: 101;
        cursor: w-resize;

        &:hover{
          background-color: rgb(#7756FF,.4);
        }

        &.is-collapsed{
          cursor: e-resize;
        }
      }
    }

    &-panel{
      .el-collapse-item__content{
        display: flex;
        flex-wrap: wrap;
        row-gap: 16px;
        column-gap: 16px;
      }

      &-item{
        flex: calc(50% - 16px);
        border-radius: 5px;
        box-sizing: border-box;
        border: 1px solid #eee;
        cursor: pointer;
        padding: 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      &__name{
        font-size: 14px;
      }

      &__image{
        width: 100%;
        height: 100%;
        & > img{
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
</style >
