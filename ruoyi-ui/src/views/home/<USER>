import aboutCover from './assets/cover/about.png';
import canlendarCover from './assets/cover/calendar.png';
import calnedarInfoCover from './assets/cover/canlendarinfo.png';
import flowCover from './assets/cover/flow.png';
import monitorCover from './assets/cover/monitor.png';
import infoCover from './assets/cover/info.png';
import insideCover from './assets/cover/inside.png';
import outletCover from './assets/cover/outlet.png';
import workCover from './assets/cover/work.png';
import calendareventCover from './assets/cover/calendarevent.png';

export const AsyncComponent = {
  calendar: () => import('./components/Calendar.vue'),
  info: () => import('./components/Info.vue'),
  work: () => import('./components/Work.vue'),
  outlet: () => import('./components/Link.vue'),
  inside: () => import('./components/Link.vue'),
  about: () => import('./components/About.vue'),
  flow: () => import('./components/Flow.vue'),
  monitor: () => import('./components/Monitor.vue'),
  calendarinfo: () => import('./components/CalendarInfo.vue'),
  calendarevent: () => import('./components/CalendarEvent.vue'),
};

export const componentCovers = {
  calendar: canlendarCover,
  info: infoCover,
  work: workCover,
  outlet: outletCover,
  inside: insideCover,
  about: aboutCover,
  flow: flowCover,
  monitor: monitorCover,
  calendarinfo: calnedarInfoCover,
  calendarevent: calendareventCover,
};

export const componentSizes = ['normal', 'middle', 'small'];

export const sizeValidator = (value) => {
  return componentSizes.includes(value);
};

export const sizeProp = {
  type: String,
  default: 'normal',
  validator: sizeValidator,
};

const widthCol = 2;
export const cols = 12 * widthCol;

export function formatLayoutItem(item) {
  if (!item.grid) {
    item.grid = {};
  }
  const grid = {
    ...item.grid,
    minW: item.grid.minW ? (item.grid.minW || 0) * widthCol : 1,
    w: (item.grid.w || 0) * widthCol,
    maxW: item.grid.maxW ? (item.grid.maxW || 0) * widthCol : cols,
    x: (item.grid.x || 0) * widthCol,
  };
  const componentProps = {
    size: 'normal',
    ...(item.componentProps || {}),
  };
  return {
    ...item,
    grid,
    ...grid,
    i: Math.random().toString(36).slice(2, 10),
    resizeableProps: {
      size: componentProps.size,
      ...(item.resizeableProps || {}),
    },
    componentProps,
  };
}

export function formatLayout(layout) {
  return layout.map(formatLayoutItem);
}

export const defaultLayout = formatLayout([
  // {
  //   key: 'calendarevent',
  //   grid: { x: 0, y: 0, w: 4, h: 33, minW: 4,maxW: 4,minH: 33,maxH: 33},
  //   componentProps: {
  //     size: 'normal'
  //   },
  //   resizeableProps: {
  //     direction: 'vertical'
  //   }
  // },
  {
    key: 'calendar',
    grid: { x: 0, y: 0, w: 3, h: 22 },
    componentProps: {
      size: 'normal',
    },
    resizeableProps: {
      direction: 'vertical',
    },
  },
  {
    key: 'info',
    grid: { x: 0, y: 22, w: 3, h: 11 },
    componentProps: {
      size: 'middle',
    },
    resizeableProps: {
      direction: 'vertical',
    },
  },
  {
    key: 'work',
    grid: { x: 3, y: 0, w: 6, h: 26 },
    componentProps: {
      size: 'normal',
    },
    resizeableProps: {
      direction: 'full',
    },
  },
  {
    key: 'outlet',
    grid: { x: 3, y: 18, w: 3, h: 7 },
    componentProps: {
      title: '站外链接',
      kind: 'outlet',
      size: 'normal',
    },
    resizeableProps: {
      direction: 'horizontal',
    },
  },
  {
    key: 'inside',
    grid: { x: 6, y: 18, w: 3, h: 7 },
    componentProps: {
      title: '站内链接',
      kind: 'inside',
      size: 'normal',
    },
    resizeableProps: {
      direction: 'horizontal',
    },
  },
  {
    key: 'about',
    grid: { x: 9, y: 0, w: 3, h: 8 },
    componentProps: {
      size: 'normal',
    },
    resizeableProps: {
      isResizable: false,
    },
  },
  {
    key: 'flow',
    grid: { x: 9, y: 12.5, w: 3, h: 16 },
    componentProps: {
      size: 'normal',
    },
    resizeableProps: {
      direction: 'vertical',
    },
  },
  {
    key: 'monitor',
    grid: { x: 9, y: 38.5, w: 3, h: 9 },
    componentProps: {
      size: 'small',
    },
    resizeableProps: {
      direction: 'vertical',
    },
  },
]);

export const componentList = [
  {
    label: '日历',
    children: [
      {
        label: '日历',
        cover: 'calendar.png',
        data: {
          key: 'calendar',
          grid: { x: 0, y: 0, w: 3, h: 22 },
          componentProps: {
            size: 'normal',
          },
          resizeableProps: {
            direction: 'vertical',
          },
        },
      },
      {
        label: '日历信息',
        cover: 'canlendarinfo.png',
        data: {
          key: 'calendarinfo',
          grid: { x: 0, y: 0, w: 3, h: 22 },
          componentProps: {
            size: 'normal',
          },
          resizeableProps: {
            direction: 'vertical',
          },
        },
      },
      {
        label: '日程日历',
        cover: 'calendarevent.png',
        data: {
          key: 'calendarevent',
          grid: { x: 0, y: 0, w: 4, h: 33 },
          componentProps: {
            size: 'normal',
          },
          resizeableProps: {
            direction: 'vertical',
          },
        },
      },
    ],
  },
  {
    label: '信息',
    children: [
      {
        label: '用户信息',
        cover: 'about.png',
        data: {
          key: 'about',
          grid: { x: 9, y: 0, w: 3, h: 8 },
          componentProps: {
            size: 'normal',
          },
          resizeableProps: {
            isResizable: false,
          },
        },
      },
      {
        label: '信息中心',
        cover: 'info.png',
        data: {
          key: 'info',
          grid: { x: 0, y: 22, w: 3, h: 11 },
          componentProps: {
            size: 'normal',
          },
          resizeableProps: {
            direction: 'vertical',
          },
        },
      },
      {
        label: '流程中心',
        cover: 'flow.png',
        data: {
          key: 'flow',
          grid: { x: 9, y: 12.5, w: 3, h: 16 },
          componentProps: {
            size: 'normal',
          },
          resizeableProps: {
            direction: 'vertical',
          },
        },
      },
      {
        label: '监控中心',
        cover: 'monitor.png',
        data: {
          key: 'monitor',
          grid: { x: 9, y: 38.5, w: 3, h: 9 },
          componentProps: {
            size: 'small',
          },
          resizeableProps: {
            direction: 'vertical',
          },
        },
      },
    ],
  },
  {
    label: '待办工作',
    children: [
      {
        label: '待办工作',
        cover: 'work.png',
        data: {
          key: 'work',
          grid: { x: 3, y: 0, w: 6, h: 26 },
          componentProps: {
            size: 'normal',
          },
          resizeableProps: {
            direction: 'full',
          },
        },
      },
    ],
  },
  {
    label: '链接',
    children: [
      {
        label: '站外链接',
        cover: 'outlet.png',
        data: {
          key: 'outlet',
          grid: { x: 3, y: 18, w: 3, h: 7 },
          componentProps: {
            title: '站外链接',
            kind: 'outlet',
            size: 'normal',
          },
          resizeableProps: {
            direction: 'horizontal',
          },
        },
      },
      {
        label: '站内链接',
        cover: 'inside.png',
        data: {
          key: 'inside',
          grid: { x: 6, y: 18, w: 3, h: 7 },
          componentProps: {
            title: '站内链接',
            kind: 'inside',
            size: 'normal',
          },
          resizeableProps: {
            direction: 'horizontal',
          },
        },
      },
    ],
  },
];
