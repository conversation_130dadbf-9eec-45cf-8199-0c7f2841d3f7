<template>
  <div class="home">
    <div class="home-item1">
      <Calendar class='calendar' size="normal" />
      <Info class="info" size="middle"></Info>
    </div>
    <div class="home-item2">
      <Work class="work" size="normal"></Work>
      <div class="links">
        <Link title="站外链接" kind="outlet" size="normal">
        </Link>
        <Link title="站内链接" kind="inside" size="normal">
        </Link>
      </div>
    </div>
    <div class="home-item3">
      <About class="about" size="normal"></About>
      <Flow size="normal"></Flow>
      <Monitor class="monitor" size="small"></Monitor>
    </div>
  </div>
</template>

<script>
import Calendar from "./components/Calendar";
import Info from "./components/Info";
import Link from "./components/Link";
import About from "./components/About";
import Work from "./components/Work";
import Flow from "./components/Flow";
import Monitor from "./components/Monitor";
import CalendarInfo from "./components/CalendarInfo";
import CalendarEvent from "./components/CalendarEvent";
import calendarInfoMixin from "./minxins/calendar-info.mixin.js";
import notifyMixin from "./minxins/notify-mixin.js";

export default {
  name: "Index",
  components: {
    Calendar,
    Info,
    Link,
    About,
    Work,
    Flow,
    Monitor,
    CalendarInfo,
    CalendarEvent,
  },
  mixins: [calendarInfoMixin, notifyMixin],
  data() {
    return {};
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.home {
  height: calc(100vh - 84px);
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #f1f1f1;

  &-item1 {
    width: 418px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow: hidden;
    .calendar {
      height: 500px;
    }
    .info {
      flex: 1;
      flex-shrink: 0;
      overflow: hidden;
    }
  }

  &-item2 {
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow: hidden;

    .work {
      flex: 1;
      overflow: hidden;
    }

    .links {
      flex-shrink: 0;
      display: flex;
      gap: 12px;
      height: 152px;
    }
  }

  &-item3 {
    width: 360px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow: hidden;

    .about {
      height: 176px;
      flex-shrink: 0;
    }

    .monitor {
      height: 200px;
      flex-shrink: 0;
    }
  }
}
</style>
<style lang="scss" src="./styles/base.scss"></style>
<style lang="scss" src="./styles/blue-theme.scss"></style>
