<template>
  <div
    ref="root"
    class="workbench-link"
    :class="[
      `is-${size}`,
      {
        'is-draggable': isDraggable,
      },
    ]"
  >
    <header class="workbench-link-header">
      <h4 class="workbench-link__title">{{ title }}</h4>
      <div v-if="!(size === 'small')" class="workbench-link__button">
        <el-button
          type="text"
          :disabled="isDraggable"
          @click="createState.visible = true"
        >
          <i class="el-icon-plus" />
          添加链接
        </el-button>
      </div>
    </header>
    <Empty class="workbench-link-main" :empty="!data.length">
      <div
        v-for="item in data"
        :key="item.uuid"
        :title="item.title"
        class="workbench-link-item"
        @click="jump(item.url)"
      >
        <div class="workbench-link__icon" :class="item.avatarName"></div>
        <div class="workbench-link__label">{{ item.title }}</div>
        <div class="workbench-link-item__close" @click.stop="delLink(item.id)">
          <span class="el-icon-circle-close" />
        </div>
      </div>
    </Empty>

    <el-dialog
      :title="dialogTitle"
      :visible="createState.visible"
      :show-close="false"
      :append-to-body="true"
      width="30%"
    >
      <div>
        <h5 class="workbench-link-icon__label">请选择图标:</h5>
        <div class="workbench-link-icon-wrapper">
          <div class="workbench-link-icon-selector">
            <div
              v-for="i in 8"
              :key="`workbench-link__icon-${i}`"
              :class="[`is-bg-${i}`]"
              class="workbench-link__icon"
              @click="createState.formData.avatarName = `is-bg-${i}`"
            />
          </div>
          <div class="workbench-link-icon__default" title="默认图标">
            <div
              class="workbench-link__icon"
              :class="createState.formData.avatarName"
            />
          </div>
        </div>
      </div>
      <template v-if="isOutlet">
        <el-input
          v-model="createState.formData.linkName"
          size="medium"
          placeholder="请输入链接名称"
          style="margin: 10px 0"
        />
        <el-input
          v-model="createState.formData.linkUrl"
          size="medium"
          placeholder="请输入链接地址"
        />
        <span style="margin-top: 10px; display: inline-block"
          >快捷添加常用链接：</span
        >
        <div>
          <el-row
            class="link-row"
            :gutter="10"
            v-for="i in commonList"
            :key="i.type"
          >
            <el-col span="4" class="link-type">{{ i.type }}</el-col>
            <el-col span="19">
              <span
                class="link-item"
                v-for="(j, index) in i.detail"
                :key="index"
                @click="inputSelect(j)"
              >
                {{ j.title }}
              </span>
            </el-col>
          </el-row>
        </div>
      </template>
      <template v-else>
        <el-input
          placeholder="输入关键字进行过滤"
          v-model="filterText"
          style="margin-top: 10px"
        >
        </el-input>
        <el-tree
          v-if="commonList.length > 1"
          ref="tree"
          class="tree"
          style="max-height: 480px; overflow-y: auto"
          :props="{
            children: 'children',
            label: label,
          }"
          :data="commonList"
          :show-checkbox="false"
          @node-click="handleNodeClick"
          :filter-node-method="filterNode"
        >
        </el-tree>
      </template>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleCreateLink">添加</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  addQuickLinkOut,
  getQuickLinks,
  addQuickLink,
  getQuickLinkOut,
  delQuickLink,
  getCommonLink,
  getQuickLink,
} from "@/api/other.js";
import Empty from "./Empty.vue";
export default {
  name: "Link",
  components: {
    Empty,
  },
  props: {
    title: {
      type: String,
    },
    kind: {
      type: String,
      default: "inside",
      validator(value) {
        return ["inside", "outlet"].includes(value);
      },
    },
    isDraggable: {
      type: Boolean,
    },
  },
  data() {
    return {
      size: "normal",
      resizeOb: null,
      data: [],
      filterText: "",
      commonList: [],
      currentNode: null,
      createState: {
        loading: false,
        visible: false,
        formData: {
          linkName: "",
          avatarName: "is-bg-1",
          linkUrl: "",
          linkColor: "",
        },
      },
    };
  },
  created() {
    this.getData();
    this.getList();
  },
  mounted() {
    const resizeOb = new ResizeObserver(this.watchResize.bind(this));
    resizeOb.observe(this.$refs.root);
    this.resizeOb = resizeOb;
  },
  beforeDestroy() {
    this.destoryOb();
  },
  methods: {
    label(data) {
      return data.meta.title;
    },
    getList() {
      if (this.isOutlet) {
        // getCommonLink().then((res) => {
        //   if (res.code === 200) {
        this.commonList = [];
        //   }
        // });
      } else {
        this.commonList = (
          this.$store?.state.permission.addRoutes || []
        ).filter((x) => !x.hidden);
      }
    },
    getData() {
      getQuickLinks().then((res) => {
        if (res.code === 200) {
          this.data = res.data.filter(
            (x) => x.linkType === (this.isOutlet ? 2 : 1)
          );
        }
      });
    },
    handleCreateLink() {
      if (this.isOutlet) {
        this.createOutletlink();
      } else {
        this.createInsidelink();
      }
    },
    getLatestParams() {
      return {
        title: this.createState.formData.linkName || "",
        url: this.createState.formData.linkUrl || "",
        routePath: this.createState.formData.linkUrl || "",
        icon: this.createState.formData.avatarName || "is-bg-1",
        // color: this.createState.formData.linkColor || "#000",
      };
    },
    createOutletlink() {
      if (this.linkName === "" || this.linkUrl === "") {
        this.$message.error("请输入链接名称和链接地址");
        return;
      }
      const params = this.getLatestParams();
      addQuickLink({ ...params, linkType: 2 }).then((res) => {
        if (res.code === 200) {
          this.$message.success("添加成功");
          this.getData();
          this.handleCancel();
        } else {
          this.$message.error("添加失败");
        }
      });
    },
    createInsidelink() {
      if (!this.currentNode) {
        this.$message.warning("请选择站内链接");
        return;
      }
      if (this.currentNode.children?.length > 0) {
        this.$message.warning("请选择一个子节点");
        return;
      }
      let params = this.getLatestParams();
      addQuickLink({ ...params, linkType: 1 }).then((res) => {
        if (res.code === 200) {
          this.$message.success("添加成功");
          this.getData();
          this.handleCancel();
        } else {
          this.$message.error("添加失败");
        }
      });
    },
    delLink(uuid) {
      delQuickLink(uuid).then((res) => {
        if (res.code === 200) {
          this.$message.success("删除成功");
          this.getData();
        } else {
          this.$message.error("删除失败");
        }
      });
    },
    handleCancel() {
      this.filterText = "";
      this.currentNode = null;
      this.createState = {
        loading: false,
        visible: false,
        formData: {
          linkName: "",
          avatarName: "is-bg-1",
          linkUrl: "",
        },
      };
    },
    inputSelect(j) {
      this.createState.formData = {
        ...this.createState.formData,
        linkName: j.title,
        linkUrl: j.code,
      };
    },
    handleNodeClick(data) {
      this.currentNode = data;
      this.createState.formData = {
        // ...data,
        ...this.createState.formData,
        linkName: data.meta.title,
        linkUrl: data.name,
      };
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.meta.title.indexOf(value) !== -1;
    },
    removeObjectsWithTypeOne(arr, prop, value) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].children) {
          arr[i].children = this.removeObjectsWithTypeOne(
            arr[i].children,
            prop,
            value
          );
        }
        if (arr[i][prop] === value) {
          arr.splice(i, 1);
          i--;
        }
      }
      return arr;
    },
    jump(code) {
      if (this.isOutlet) {
        const a = document.createElement("a");
        a.href = code;
        a.target = "_blank";
        a.click();
      } else {
        this.$router.push({ name: code });
        // history.pushState({}, "", code);
        // this
      }
    },
    destoryOb() {
      if (this.resizeOb) {
        this.resizeOb.disconnect();
      }
    },
    watchResize([entire]) {
      if (entire.contentRect.width >= 550) {
        this.size = "normal";
      } else if (entire.contentRect.width >= 150) {
        this.size = "middle";
      } else {
        this.size = "small";
      }
    },
  },
  computed: {
    dialogTitle() {
      return `添加${this.title}`;
    },
    isOutlet() {
      return this.kind === "outlet";
    },
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
};
</script>

<style lang="scss">
.workbench-link {
  width: 100%;
  height: 100%;
  padding: 28px 20px 10px;
  box-sizing: border-box;
  background: #fff;
  box-shadow: 1px 2px 13px 1px rgba(139, 180, 225, 0.11);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  position: relative;

  &.is-normal {
    .workbench-link-main {
      align-items: flex-start;
    }
  }

  &.is-small {
    padding: 16px;
    .workbench-link-header {
      text-align: center;
      justify-content: center;
    }
    .workbench-link-main {
      column-gap: 0;
    }
    .workbench-link-item {
      min-width: 100px;
    }
  }

  &.is-draggable {
    .workbench-link-main {
      overflow-x: hidden;
    }
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__title {
    font-family: Source Han Sans CN Bold, serif;
    font-weight: bold;
    font-size: 18px;
    color: rgba(#333333, 0.95);
  }

  &-main {
    margin-top: 20px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    column-gap: 14px;
    overflow-x: auto;
    scroll-snap-type: x proximity;
    padding-bottom: 12px;

    &::-webkit-scrollbar {
      margin-top: 3px;
      height: 5px; /* 滚动条的高度 */
    }
  }

  &-item {
    flex: 1;
    max-width: 80px;
    min-width: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    scroll-snap-align: center;
    position: relative;

    &:hover {
      .workbench-link-item__close {
        display: block;
      }
    }

    &__close {
      display: none;
      color: #825ce4;
      font-size: 12px;
      cursor: pointer;
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
    }
  }

  &__icon {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 22px;
    line-height: 40px;
    text-align: center;
    border-radius: 8px;
    background-image: url("../assets/link/link-bg-1.png");
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;

    &::before {
      content: "";
      display: block;
      position: absolute;
      width: 26px;
      height: 26px;
      background-image: url("../assets/link/link-icon-1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    @for $i from 1 through 8 {
      &.is-bg-#{$i} {
        background-image: url("../assets/link/link-bg-#{$i}.png");

        &::before {
          background-image: url("../assets/link/link-icon-#{$i}.png");
        }
      }
    }
  }

  &__label {
    margin-top: 8px;
    font-family: Source Han Sans CN, serif;
    font-weight: 600;
    font-size: 12px;
    color: #202225;
    max-width: calc(100% - 10px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  &__button {
    .el-button {
      color: #999;
      padding: 0;
    }
  }

  &-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-icon__default {
    display: inline-block;
    padding-left: 16px;
    border-left: 1px solid #eeeeee;
  }
  &-icon__label {
    font-size: 14px;
    margin-bottom: 10px;
  }

  &-icon-selector {
    display: flex;
    flex-wrap: wrap;
    row-gap: 12px;
    column-gap: 6px;

    & > div {
      cursor: pointer;
    }
  }
  &::before,
  &::after {
    content: "";
    width: 29px;
    height: 29px;
    background: url("../assets/card-horn-decorate.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    bottom: 0;
  }
  &::after {
    left: initial;
    right: 0;
    transform: rotateZ(-90deg);
  }
}

.link-row {
  margin-top: 20px;
  padding-bottom: 10px;

  .link-type {
    color: #202225;
    border-right: #202225 1px solid;
    font-size: 14px;
  }

  .link-item {
    color: rgba(#202225, 0.5);
    display: inline-block;
    font-weight: 400;
    margin-right: 10px;
    cursor: pointer;
    margin-bottom: 5px;
  }

  .link-item:hover {
    color: #825ce4;
  }

  .link-type:last-child {
    border-right: none;
  }

  .link-type.active {
    color: #825ce4;
  }
}
</style>
