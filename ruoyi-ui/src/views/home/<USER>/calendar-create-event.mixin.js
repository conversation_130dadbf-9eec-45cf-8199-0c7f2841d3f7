import dayjs from "dayjs";
import {addCalendar} from "@/api/workCalendar"
import { cloneDeep } from "lodash"

// 标签映射
const labelMap = {
  title: '日程名称',
  content: '日程内容',
  date: '时间范围'
}

// 初始化表单数据
const initFormData = {
  title: '',
  content: '',
  date: []
}

export default {
  name: 'CalendarCreateEventMixin',
  inject: ['updateInfoData'],
  data: () => {
    return {
      // 标签映射
      eventLabelMap: labelMap,
      // 规则
      eventFormRules: {
        title: [
          {
            required: true,
            message: `请输入${labelMap.title}`
          }
        ],
        content: [
          {
            required: true,
            message: `请输入${labelMap.content}`
          }
        ],
        date: [
          {
            required: true,
            message: `请选择${labelMap.date}`
          }
        ]
      },
      // 添加日程Dialog数据
      eventState: {
        visible: false,
        loading: false,
        formData: cloneDeep(initFormData)
      }
    }
  },
  methods: {
    // 添加日程信息
    handleCreateEvent(){
      this.$refs['eventForm'].validate((valid) => {
        if (valid) {
          this.eventState.loading = true
          const params = {
            taskName: this.eventState.formData.title,
            taskDesc: this.eventState.formData.content,
            // date: this.currentDay,
            taskStartTime: dayjs(this.eventState.formData.date[0]).format('YYYY-MM-DD HH:mm:ss'),
            taskEndTime: dayjs(this.eventState.formData.date[1]).format('YYYY-MM-DD HH:mm:ss')
          }
          addCalendar(params).then(res => {
            if (res.code === 200) {
              this.$message.success('添加成功')
              this.handleCreateEventClose()
              this.getData()
              this.updateInfoData()
            } else {
              this.$message.error('添加失败')
            }
          }).finally(() =>{
            this.eventState.loading = false
          })
        } else {
          return false;
        }
      });

    },
    // 处理创建事件打开
    handleCreateEventOpen() {
      this.eventState.formData = cloneDeep(initFormData)
      setTimeout(() => {
        this.$refs['eventForm']?.clearValidate()
      }, 0)
      this.eventState.visible = true
    },
    // 处理创建事件打开
    handleCreateEventClose() {
      this.eventState.visible = false
    }
  }
}
