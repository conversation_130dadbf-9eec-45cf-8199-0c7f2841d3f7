<template>
  <div
    ref='root'
    v-bind='$attrs'
    :class='["workbench-empty",`is-${finalSize}`,$attrs.class,{
      "is-empty": empty,
    }]'
  >
    <slot v-if='!empty'></slot>
    <template v-else>
      <div class='workbench-empty__text'>{{text}}</div>
    </template>
  </div>
</template>

<script>
export default {
  name: "Empty",
  props: {
    text: {
      type: String,
      default: '暂无数据'
    },
    empty: {
      type: Boolean,
      default: false
    },
    auto: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: '',
      validator(val){
        return ['','size','large'].includes(val)
      }
    },
  },
  mounted() {
    this.observerSize()
  },
  beforeDestroy() {
    this.destoryOb()
  },
  data(){
    return {
      resizeOb: null,
      internalSize: 'nromal'
    }
  },
  computed:{
    finalSize(){
      return this.size || this.internalSize
    }
  },
  methods: {
    watchResize([entire]){
      if (entire.contentRect.height >= 300){
        this.internalSize = 'large'
      } else if (entire.contentRect.height >= 160){
        this.internalSize = 'normal'
      } else if (entire.contentRect.height >= 120){
        this.internalSize = 'small'
      } else {
        this.internalSize = 'extra-small'
      }
    },
    observerSize(){
      if (this.size || !this.empty){
        this.destoryOb()
        return
      }
      const resizeOb = new ResizeObserver(this.watchResize.bind(this))
      resizeOb.observe(this.$refs.root)
      this.resizeOb = resizeOb
    },
    destoryOb(){
      if (this.resizeOb){
        this.resizeOb.disconnect()
      }
    }
  },
  watch:{
    empty(val){
      if (val){
        this.observerSize()
      } else {
        this.destoryOb()
      }
    }
  }
}
</script>

<style lang='scss'>
  .workbench-empty{
    position: relative;
    &.is-empty{
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }

    &.is-small{
      .workbench-empty__text{
        width: 140px;
        height: 116px;
        padding-top: 20px;
        font-size: 12px;
      }
    }

    &.is-large{
      .workbench-empty__text{
        width: 320px;
        height: 264px;
        padding-top: 44px;
        font-size: 18px;
      }
    }

    &.is-extra-small{
      .workbench-empty__text{
        width: 90px;
        height: 74px;
        padding-top: 7px;
        font-size: 10px;
      }
    }

    &__text{
      flex: none;
      width: 200px;
      height: 165px;
      box-sizing: border-box;
      padding-top: 34px;
      background-size: 100% 100%;
      background-image: url("../assets/empty-bg.png");
      background-repeat: no-repeat;
      font-size: 14px;
      color: rgba(#7756FF,.6);
      text-align: center;
    }
  }
</style>
