<template>
  <div class='workbench-work-result'>
    <span v-if="showText" :class='{"is-success":isCompleted}' class='workbench-work-result-status'>
      {{isCompleted ? '已完成' : '未完成'}}
    </span>
    <template v-else>
      <el-radio-group
        v-if="isNormal"
        :value="data.completeState"
        @input='handleOperateInStep'
      >
        <el-radio label="1">完成</el-radio>
        <el-radio label="0">无业务</el-radio>
        <el-radio label="2">其他</el-radio>
      </el-radio-group>
      <el-dropdown
        v-else
        trigger="click"
        size='small'
        @command='handleOperateInStep'
      >
        <span class="el-dropdown-link">
          <i class="el-icon-more-outline"></i>
        </span>
        <el-dropdown-menu slot='dropdown'>
          <el-dropdown-item command='0'>
            无业务
          </el-dropdown-item>
          <el-dropdown-item command='1' >
            完成
          </el-dropdown-item>
          <el-dropdown-item command='2' >
            其他
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </template>
  </div>
</template>

<script>
import dayjs from "dayjs";

export default {
  name: "WorkSuccessResult",
  props: {
    data: {
      required: true
    },
    parentData: {
      required: true
    },
    isNormal: {
      required: true
    }
  },
  emits: ['on-operate'],
  methods: {
    handleOperateInStep(val){
      if (dayjs().valueOf() < dayjs(`${this.parentData.startDate} ${this.parentData.startTime}`).valueOf()) {
        this.$message.warning('待办工作尚未开始')
        return
      }
      this.$emit('on-operate',this.parentData.id,this.data,val)
    }
  },
  computed: {
    showText(){
      let endDatetime = dayjs(`${this.parentData.endDate} ${this.parentData.endTime}`);
      if (dayjs().isAfter(endDatetime)){
        return true
      }
      return this.data.completeState && this.data.completeState !== '2'
    },
    isCompleted(){
      return !!this.data.completeState
    }
  }
}
</script>

<style lang='scss'>
.workbench-work-result{
  &-status{
    padding: 2px 8px;
    font-family: Source Han Sans CN,serif;
    font-weight: 400;
    font-size: 12px;
    border-radius: 4px;
    color: #FF6666;
    border: 1px solid #FF6565;
    background: rgba(255,101,101,0.12);

    &.is-success{
      background: rgba(0,207,112,0.12);
      border: 1px solid #17A377;
      color: #17A377;
    }
  }
}
</style>
