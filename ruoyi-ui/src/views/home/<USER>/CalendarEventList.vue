<template>
  <div :style='wrapperStyle'>
    <ul class='workbench-calendar-event-list'>
      <li v-for='(item,i) in list' :key='`workbench-calendar-event-${i}`' class='workbench-calendar-event-list-item'>
        <header class='workbench-calendar-event-list-head'>
          <span class='workbench-calendar-event__icon'></span>
          <span class='workbench-calendar-event__title'>事项{{i+1}}</span>
          <span class='workbench-calendar-event__time'>{{item.startTime}}-{{item.endTime}}</span>
        </header>
        <p class='workbench-calendar-event__name'>{{item.content}}</p>
      </li>
    </ul>
    <div
      class='workbench-calendar-event__mask'
      @click='handleClose'
    />
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      required:true,
    },
    position: {
      type: Array,
      required: true
    },
    destroyInstance: Function
  },
  emits: ['on-close'],
  computed: {
    wrapperStyle({position}){
      return {
        'position': 'fixed',
        'top': position[1] + 'px',
        'left': position[0] + 'px'
      }
    }
  },
  methods: {
    handleClose() {
      this.$destroy()
      this.$el.parentNode.removeChild(this.$el)
      this.destroyInstance?.()
    }
  },
}
</script>
