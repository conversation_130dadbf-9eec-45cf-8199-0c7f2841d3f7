<template>
  <section
    class="workbench-calendar workbench-calendar-event"
    :class="[`is-${size}`]"
  >
    <header class="workbench-calendar-header">
      <ul class="workbench-calendar-tags">
        <li
          v-for="item in shareType"
          :key="item.value"
          class="workbench-calendar-tags__item"
          :class="[`is-${item.value}`]"
        >
          {{ item.label }}
        </li>
      </ul>

      <button
        v-show="schedulingIsShow"
        class="workbench__button"
        style="margin-left: auto; margin-right: -12px"
        @click="jumpWork"
      >
        值班管理
      </button>
      <el-button
        class="workbench-calendar__event"
        type="text"
        size="small"
        icon="el-icon-plus"
        @click="handleCreateEventOpen"
      >
        添加日程
      </el-button>
    </header>

    <section class="workbench-calendar-info">
      <div class="workbench-calendar__date">{{ displayMonth }}</div>
      <div
        class="workbench-calendar__day"
        @click="toToday"
        style="margin-left: auto; margin-right: 16px"
      >
        今天
      </div>
      <div class="workbench-calendar__prev" @click="prevHalfMonth">
        <span class="el-icon-arrow-left"></span>
      </div>
      <div class="workbench-calendar__next" @click="nextHalfMonth">
        <span class="el-icon-arrow-right"></span>
      </div>
    </section>

    <main class="workbench-calendar-event-main">
      <section
        v-for="day in composeEventToCalendar"
        :key="day.fullDate"
        :class="{
          'is-reset': day.isRestDay && !getAdjustRest(day.fullDate),
          'is-prev': day.isPreviousMonth,
          'is-next': day.isNextMonth,
          'is-today': displayDay === day.fullDate,
          ...getHolidayClass(day.fullDate),
        }"
        class="workbench-calendar-event-item"
        @click="changeDate(day.fullDate)"
      >
        <header class="workbench-calendar-event-item-head">
          <h4 class="workbench-calendar-event__week">{{ day.weekDay }}</h4>
          <i class="workbench-calendar-dots">
            <i
              v-for="item in getDots(day.fullDate)"
              :key="`workbench-calenday-dot-${day.fullDate}-${item}`"
              :class="item"
            />
          </i>
          <h4 class="workbench-calendar-event__day">{{ day.day }}</h4>
        </header>
        <main
          class="workbench-calendar-event-item-main"
          @click="(e) => handleOpenEventList(e, day)"
        >
          <div
            v-for="(item, index) in 4"
            :key="index"
            class="workbench-calendar-event__name"
          >
            <el-tooltip
              v-if="day.eventList[index]"
              :content="day.eventList[index].title"
              placement="top"
            >
              <span>{{ day.eventList[index].title }}</span>
            </el-tooltip>
          </div>
        </main>
      </section>
    </main>

    <section class="workbench-calendar-footer">
      <div
        v-for="item in filtedFooterList"
        :key="item.value"
        class="workbench-calendar-footer__item"
      >
        <span
          class="workbench-calendar-footer__dot"
          :class="[`is-${item.value}`]"
        ></span>
        <span class="workbench-calendar-footer__label">{{ item.label }}</span>
      </div>
    </section>

    <el-dialog
      :visible="eventState.visible"
      :append-to-body="true"
      title="添加日程"
      width="30%"
      @close="handleCreateEventClose"
    >
      <el-form
        ref="eventForm"
        :model="eventState.formData"
        :rules="eventFormRules"
        label-width="80px"
      >
        <el-form-item prop="title" :label="eventLabelMap.title">
          <el-input
            v-model="eventState.formData.title"
            :maxlength="15"
            :show-word-limit="true"
            :placeholder="eventLabelMap.title"
          />
        </el-form-item>
        <el-form-item prop="content" :label="eventLabelMap.content">
          <el-input
            v-model="eventState.formData.content"
            type="textarea"
            :rows="3"
            :maxlength="300"
            :show-word-limit="true"
            :placeholder="eventLabelMap.content"
          />
        </el-form-item>
        <el-form-item prop="date" :label="eventLabelMap.date">
          <el-date-picker
            v-model="eventState.formData.date"
            type="datetimerange"
            style="width: 100%"
            popper-class="scale-picker-panel"
            :append-to-body="false"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd HH:mm"
            :default-time="['08:30:00', '17:30:00']"
          />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="handleCreateEventClose">取消</el-button>
        <el-button
          :loading="eventState.loading"
          type="primary"
          @click="handleCreateEvent()"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </section>
</template>

<script>
import Vue from "vue";
import dayjs from "dayjs";
import { getCalendar, getCalendarHolidays } from "../../../api/workCalendar";
import calendarBaseMixin from "../minxins/calendar-base-mixin";
import calendarCreateEvent from "../minxins/calendar-create-event.mixin";
import { generateEventCalendar } from "../utils/calendar-helper";
import CalendarEventList from "./CalendarEventList.vue";

const currentDay = dayjs();
const defaultCalendar = generateEventCalendar(currentDay);

export default {
  name: "Calendar",
  mixins: [calendarBaseMixin, calendarCreateEvent],
  props: {
    isDraggable: {
      type: Boolean,
    },
  },
  data: () => {
    return {
      currentDay: currentDay,
      calendar: defaultCalendar,
      holidayList: [],
      internalHolidayList: [],
      eventList: [],
    };
  },
  created() {
    this.getData();
  },
  computed: {
    filtedFooterList() {
      return this.footerList.filter((item) => item.value !== "event");
    },
    displayMonth({ currentDay }) {
      return currentDay.format("YYYY年MM月");
    },
    composeEventToCalendar() {
      return this.calendar.map((item) => {
        return {
          ...item,
          eventList: this.getEventListByDate(item.fullDate),
        };
      });
    },
    getResetFullDate() {
      return this.calendar[4].fullDate;
    },
    displayDay() {
      return this.currentDay.format("YYYY-MM-DD");
    },
  },
  methods: {
    getData() {
      const params = [
        this.calendar[0].fullDate,
        this.calendar[this.calendar.length - 1].fullDate,
      ];
      getCalendar(params[0], params[1]).then((res) => {

        if (res.code.toString() === "200") {
          this.holidayList = res.data.todoList;
          this.eventList = res.data.arrange;
        }
      });
      getCalendarHolidays().then((res) => {
        console.log("🚀 ~ getCalendarHolidays ~ res:", res);

        if (res.code.toString() === "200") {
          this.internalHolidayList = res.data;
        }
      });
    },
    toToday() {
      this.currentDay = dayjs();
      this.updateCalendar();
    },
    changeDate(date) {
      this.currentDay = dayjs(date);
    },
    prevHalfMonth() {
      this.currentDay = dayjs(this.getResetFullDate).subtract(15, "day");
      this.updateCalendar();
    },
    nextHalfMonth() {
      this.currentDay = dayjs(this.getResetFullDate).subtract(-15, "day");
      this.updateCalendar();
    },
    updateCalendar() {
      this.calendar = generateEventCalendar(this.currentDay);
      this.getData();
    },
    handleOpenEventList(e, day) {
      if (!day?.eventList?.length) {
        return;
      }
      const sourceRect =
        e.target.parentElement.parentElement.getBoundingClientRect();
      let instance;
      let el = document.createElement("div");
      document.querySelector(".vue-grid-layout").appendChild(el);
      instance = new Vue({
        render: (h) =>
          h(CalendarEventList, {
            props: {
              list: day.eventList,
              position: [sourceRect.right, sourceRect.top],
              destroyInstance: () => {
                if (el) {
                  el.remove();
                }
                instance && instance.$destroy();
                instance = null;
              },
            },
          }),
      }).$mount(el);
    },
    getEventListByDate(date) {
      return this.eventList.filter((item) => item.date === date);
    },
  },
};
</script>

<style lang="scss" src="../styles/calendar.scss"></style>
<style lang="scss">
.workbench-calendar-event {
  &-main {
    display: flex;
    flex-wrap: wrap;
    row-gap: 10px;
    column-gap: 10px;
    width: 100%;
    height: 100%;
    margin-bottom: 30px;
  }

  &-item {
    position: relative;
    width: calc((100% - 20px) / 3);
    box-sizing: border-box;
    padding: 12px 10px;
    border: 1px solid transparent;
    background-color: rgb(#e7e7ee, 0.35);
    border-radius: 3px;
    display: flex;
    flex-direction: column;
    row-gap: 12px;

    &-head {
      display: flex;
      align-items: center;
    }

    &-main {
      width: 100%;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      // display: flex;
      // flex-direction: column;
      // row-gap: 10px;
      cursor: pointer;
    }

    &.is-reset {
      border-top-color: #ffb535;
      background-color: rgba(#ffb535, 0.12);
      .workbench-calendar-event__day,
      .workbench-calendar-event__week {
        color: #ffb535;
      }
    }

    &.is-today {
      background-color: rgba(#825ce4, 0.2);
      border: 1px solid #825ce4;

      .workbench-calendar-event__day,
      .workbench-calendar-event__week {
        color: #3d2282;
      }
    }

    &.before-border {
      content: "";
      display: block;
      width: 100%;
      height: 3px;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9;
    }

    &.is-a::before {
      @extend .before-border;
      background: #2d95ff;
    }
    &.is-eft::before {
      @extend .before-border;
      background: #46ccbe;
    }
    &.is-amer::before {
      @extend .before-border;
      background: #ff8383;
    }
    &.is-a-with-eft::before {
      @extend .before-border;
      background: linear-gradient(
        to right,
        #2d95ff 50%,
        /* 左边蓝色 */ #46ccbe 50% /* 右边绿色 */
      );
    }
    &.is-a-with-amer::before {
      @extend .before-border;
      background: linear-gradient(
        to right,
        #2d95ff 50%,
        /* 左边蓝色 */ #ff8383 50% /* 右边红色 */
      );
    }
    &.is-eft-with-amer::before {
      @extend .before-border;
      background: linear-gradient(
        to right,
        #46ccbe 50%,
        /* 左边绿色 */ #ff8383 50% /* 右边红色 */
      );
    }
    &.is-all::before {
      @extend .before-border;
      background: linear-gradient(
        to right,
        #2d95ff 0%,
        /* 顶部蓝色 */ #2d95ff 33%,
        /* 蓝色 33% */ #46ccbe 33%,
        /* 中部绿色 */ #46ccbe 66%,
        /* 绿色 66% */ #ff8383 66%,
        /* 底部红色 */ #ff8383 100% /* 红色 100% */
      );
    }
  }

  &-list {
    width: 249px;
    box-sizing: border-box;
    padding: 18px;
    border-radius: 3px;
    box-shadow: -1px 2px 9px 1px rgba(129, 166, 206, 0.21);
    background-color: #fff;
    z-index: 99;
    display: flex;
    flex-direction: column;
    row-gap: 16px;

    &-head {
      display: flex;
      align-items: center;
      column-gap: 3px;
    }
  }

  &__week {
    margin-right: 6px;
    font-family: Source Han Sans CN, serif;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
  }
  &__day {
    margin-left: auto;
    font-family: "Source Han Sans CN", serif;
    font-weight: 400;
    font-size: 28px;
    color: #333333;
  }

  &__name {
    width: 100%;
    height: 19px;
    box-sizing: border-box;
    padding-bottom: 4px;
    display: flex;
    border-bottom: 1px dashed rgba(#000000, 0.12);
    span {
      max-width: 100%;
      font-weight: 400;
      font-size: 14px;
      line-height: 1;
      font-family: Source Han Sans CN, serif;
      letter-spacing: 1px;
      color: #9a9a9a;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  &__icon {
    width: 10px;
    height: 12px;
    display: inline-block;
    margin-right: 3px;
    background-image: url("../assets/event-icon.png");
    background-repeat: no-repeat;
    background-size: cover;
  }

  &__mask {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 88;
    width: 100vw;
    height: 100vh;
  }

  &__title {
    font-family: Source Han Sans CN Bold, serif;
    font-size: 14px;
    color: rgba(#333333, 0.8);
  }

  .workbench-calendar-dots {
    position: unset;
  }
}
</style>
