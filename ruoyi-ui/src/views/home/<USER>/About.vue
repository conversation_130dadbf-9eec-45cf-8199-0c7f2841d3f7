<template>
  <section class='workbench-about'>
    <div class="workbench-about-inner">
      <div class="workbench-about-content">
        <header class='workbench-about-header'>
          <h4 class='workbench-about__title'>
            欢迎使用新华资产运营管理系统
          </h4>
        </header>
        <main class='workbench-about-main'>
          <div class='workbench-about-avatar' @click="handleSelectAvatarOpen">
            <div class='workbench-about-avatar__mask'></div>
            <div class="image-container">
              <img :src='getAvatar' alt='avatar' >
            </div>
          </div>
          <div class="workbench-about-main-content">
            <div class='workbench-about__desc'>今天也要加油呀！</div>
            <div class="workbench-about-time">
              <div class="workbench-about-time-item">
                <span>{{currentYear}}</span>
                <span></span>
                <span>{{currentMonthDate}}</span>
              </div>
              <div class="workbench-about-time-item">
                <span>{{data.weekday || weekDay}}</span>
                <span></span>
                <span>农历  {{data.dateN || lunar}}</span>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
    <el-dialog
      title="选择头像"
      width="464px"
      :visible="selectAvatarVisible"
      :append-to-body='true'
      @close="handleSelectAvatarClose"
    >
      <div class="workbench-about-select-avatar-container">
        <div
          v-for="(item, index) in avatars"
          :key="item.id"
          class="workbench-about-select-avatar-item-container"
        >
          <div
            class="image-container"
            @click="handleAvatarSelect(item)"
          >
            <img :src="item.userAvatar" />
          </div>
          <div
            v-if="item.chooseIs"
            class="active-container"
          >
            <i class="el-icon-check"></i>
          </div>
          <i class="el-icon-circle-close" @click.stop="handleAvatarDelete(item, index)"></i>
        </div>
        <el-upload
          v-if="avatars.length < 8"
          action=""
          list-type="picture-card"
          accept=".jpg,.jpeg,.png,.svg,.gif"
          :file-list="avatarFileList"
          :show-file-list="false"
          :before-upload="handleAvatarBeforeUpload"
          :on-change="handleAvatarChange"
          :http-request="handleAvatarUpload"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </div>
    </el-dialog>
  </section>
</template>

<script >
  import { getWelcome, getAvatars, uploadAvatar, updateAcatar, deleteAcatar } from "@/api/welcome";
  import dayjs from "dayjs";

  const currentDay = dayjs()

  const weeks = ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

  export default {
    name: 'About',
    data(){
      return {
        data: {
          loginNums: 0,
          dateN: '',
          weekday: ''
        },
        currentYear: currentDay.format("YYYY年"),
        currentMonthDate: currentDay.format('MM月DD日'),
        weekDay: weeks[currentDay.day()],
        lunar: '',
        selectAvatarVisible: false,
        avatars: [],
        avatarFileList: []
      }
    },
    computed: {
      getAvatar() {
        return this.avatars.find((item) => item.chooseIs)?.userAvatar || require('../../../assets/images/welcome/头像01.png')
      }
    },
    created() {
      Promise.all([this.getData(), this.getAvatarData()])
      const lunisolar = new window.lunisolar()
      const char8Year = lunisolar.char8.year
      const animalYear = lunisolar.format('cZ年')
      this.lunar = `${char8Year.stem}${char8Year.stem.e5}${animalYear}  ${lunisolar.format('lMlD')}`
    },
    methods: {
      async getData() {
        // const data = await getWelcome()
        // data.code === 200 && (this.data = data.result)
      },
      // 获取头像数据
      async getAvatarData() {
        // const data = await getAvatars()
        // data.code === 200 && (this.avatars = data.result)
      },
      // 处理选择头像打开
      handleSelectAvatarOpen() {
        this.selectAvatarVisible = false
      },
      // 处理选择头像关闭
      handleSelectAvatarClose() {
        this.selectAvatarVisible = false
      },
      // 处理头像上传之前
      handleAvatarBeforeUpload(file) {
        if (file.size > 1024 * 1024) {
          this.$message.warning('头像大小不能超过 1 M')
          return false
        }
        return true
      },
      // 处理头像改变
      handleAvatarChange(_file, fileList) {
        this.avatarFileList = fileList
      },
      // 处理重置头像激活
      handleResetAvatarActive() {
        this.avatars.forEach((item) => {
          item.chooseIs = 0
        })
      },
      // 处理头像上传
      async handleAvatarUpload() {
        const params = new FormData()
        this.avatarFileList.forEach((item) => {
          params.append('file', item.raw, item.name)
        })
        const data = await uploadAvatar(params)
        this.handleResetAvatarActive()
        this.avatars.push(data.result)
        this.avatarFileList.splice(0)
      },
      // 处理头像选择
      async handleAvatarSelect(item) {
        await updateAcatar({
          id: item.id
        })
        this.handleResetAvatarActive()
        item.chooseIs = 1
      },
      // 处理头像删除
      async handleAvatarDelete(item, index) {
        await this.$confirm('是否删除该头像', '提示', {
          type: 'warning'
        }).then(async () => {
          await deleteAcatar({
            id: item.id
          })
          this.avatars.splice(index, 1)
          if (item.chooseIs && this.avatars.length) {
            const first = this.avatars[0]
            await updateAcatar({
              id: first.id
            })
            first.chooseIs = 1
          }
        }).catch(() => {})
      }
    },
  }
</script >

<style lang='scss'>
  .workbench-about{
    width: 100%;
    height: 100%;
    padding: 5px;
    box-sizing: border-box;
    border-radius: 8px;
    background-color: #fff;
    &-inner {
      width: 100%;
      height: 100%;
      display: flex;
      border-radius: 4px;
      overflow: hidden;
      // &::after {
      //   content: '';
      //   width: 83px;
      //   height: 100%;
      //   background: url('../assets/about-decorate.png') no-repeat;
      //   background-size: 100% 100%;
      // }
    }
    &-content {
      flex: 1;
      height: 100%;
      box-sizing: border-box;
      padding: 16px 8px 0;
      background-color: #dfc8e5;
    }
    &-header{
      border-bottom: 1px solid #FFFFFF;
      padding-bottom: 14px;
    }

    &__title{
      font-family: PangMenZhengDao,serif;
      font-weight: 400;
      font-size: 20px;
      letter-spacing: 1px;
      color: #9C7AB6;
      // text-align: center;
    }

    &-main{
      margin: 10px 0;
      display: flex;
      align-items: center;
      column-gap: 8px;
      &-content {
        display: flex;
        flex-direction: column;
        row-gap: 16px;
        overflow: hidden;
      }
    }

    &-avatar{
      cursor: pointer;
      width: 90px;
      height: 90px;
      overflow: hidden;
      text-align: center;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      &__mask{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-size: 90px 88px;
        background-image: url("../assets/avatar-border.png");
      }

      .image-container{
        width: 82px;
        height: 82px;
        display: flex;
        align-items: center;
        border-radius: 50%;
        overflow: hidden;
        img {
          width: 100%;
        }
      }
    }

    &__desc{
      font-weight: bold;
      font-size: 20px;
      color: #9C7AB6;
    }

    &-time{
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      &-item {
        display: flex;
        align-items: center;
        column-gap: 5px;
        span {
          &:nth-child(2) {
            width: 1px;
            height: 12px;
            background: #9C7AB6;
          }
          &:not(:nth-child(2)) {
            font-size: 16px;
            color: #9C7AB6;
            line-height: 1;
          }
        }
      }
    }
  }
  .workbench-about-select-avatar-container {
    display: flex;
    flex-wrap: wrap;
    row-gap: 8px;
    column-gap: 8px;
    .workbench-about-select-avatar-item-container {
      width: 100px;
      height: 100px;
      position: relative;
      .image-container {
        cursor: pointer;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        border: 1px solid #c0ccda;
        border-radius: 6px;
        overflow: hidden;
        img {
          width: 100%;
        }
      }
      img {
        width: 100%;
      }
      .el-icon-circle-close {
        cursor: pointer;
        font-size: 16px;
        border-radius: 50%;
        color: #ff0000;
        border-radius: 50%;
        background-color: #fff;
        position: absolute;
        top: -4px;
        right: -4px;
        &:hover {
          color: rgba(#ff0000, 0.6);
        }
      }
      .active-container {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        background-color: rgba(#000, 0.3);
        position: absolute;
        top: 0;
        left: 0;
        .el-icon-check {
          font-weight: bold;
          font-size: 24px;
          color: #67C23A;
        }
      }
    }
    .el-upload--picture-card {
      width: 100px;
      height: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style >
