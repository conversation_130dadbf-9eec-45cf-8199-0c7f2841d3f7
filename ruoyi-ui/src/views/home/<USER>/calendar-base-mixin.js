import { hasPermission2D } from "@/utils/index";
import dayjs from "dayjs";

const shareType = [
  {
    label: "A股",
    value: "a",
  },
  // {
  //   label: "港股",
  //   value: "etf",
  // },
  // {
  //   label: "美股",
  //   value: "amer",
  // },
];

const footerList = [
  {
    label: "当日值班",
    value: "on-durt",
  },
  // {
  //   label: "新股信息",
  //   value: "new-share",
  // },
  {
    label: "日程信息",
    value: "event",
  },
];

export default {
  name: "CalendarBaseMixin",
  props: {
    isDraggable: {
      type: Boolean,
    },
  },
  mounted() {
    // 判断值班管理权限
    this.schedulingIsShow = hasPermission2D(
      this.$store,
      "operateMonitor",
      "scheduling"
    );
  },
  data: () => {
    return {
      schedulingIsShow: false,
      size: "normal",
      shareType,
      footerList,
      holidayList: [],
      internalHolidayList: [],
    };
  },
  methods: {
    jumpWork() {
      this.$router.push({ path: "system/scheduling-manage" });
      // this.parentRouter.push('/qds/scheduling')
    },
    getDots(date, holidayList, eventList) {
      const result = [];
      const item = holidayList.find((item) => {
        return this.isTimeInRange(date, item.taskStartTime, item.taskEndTime);
      });
      const hasEvent = eventList.find((item) => item.date === date);

      if (!hasEvent && !item) {
        return result;
      }

      if (hasEvent) {
        result.push("is-on-durt");
      }

      if (item) {
        result.push("is-event");
      }

      // if (hasEvent) {
      //   result.push("is-event");
      // }

      // if (item?.hasNewStock) {
      //   result.push("is-new-share");
      // }

      return result;
    },
    isTimeInRange(timeStr, startTimeStr, endTimeStr) {
      // 使用 dayjs 解析时间
      const time = dayjs(timeStr, "YYYY-MM-DD");
      const startTime = dayjs(startTimeStr, "YYYY-MM-DD HH:mm:ss");
      const endTime = dayjs(endTimeStr, "YYYY-MM-DD HH:mm:ss");

      // 检查时间是否有效
      if (!time.isValid() || !startTime.isValid() || !endTime.isValid()) {
        throw new Error("无效的时间格式");
      }

      // 判断时间是否在区间内（包含边界）
      return (
        (time.isAfter(startTime) || time.isSame(startTime)) &&
        (time.isBefore(endTime) || time.isSame(endTime))
      );
    },
    canDisplayHoliday(date, internalHolidayList) {
      const item = internalHolidayList.find((item) => item === date);
      if (item) {
        return true;
      }
      return false;
      // if (!item) {
      //   return false;
      // }
      // return true;
      // const {
      //   cnholiday = false,
      //   hkholiday = false,
      //   usaholiday = false,
      // } = item || {};
      // return cnholiday || hkholiday || usaholiday;
    },
    getHolidayClass(date, internalHolidayList) {
      const item = internalHolidayList.find((item) => item === date);
      if (item) {
        return {
          "is-a": true,
        };
      }
      return {};
      // if (!item || !this.isWeekend(date)) {
      //   return {};
      // }
      // return {
      //   "is-a": true,
      // };
      // const {
      //   cnholiday = false,
      //   hkholiday = false,
      //   usaholiday = false,
      // } = item || {};
      // return {
      //   "is-a": cnholiday,
      //   "is-eft": hkholiday,
      //   "is-amer": usaholiday,
      //   "is-a-with-eft": cnholiday && hkholiday,
      //   "is-a-with-amer": cnholiday && usaholiday,
      //   "is-eft-with-amer": hkholiday && usaholiday,
      //   "is-all": cnholiday && hkholiday && usaholiday,
      // };
    },
    isWeekend(date) {
      const day = dayjs(date).day(); // 获取星期几（0-6，0是周日，6是周六）
      return day === 0 || day === 6;
    },
    getAdjustRest(date) {
      const item = this.internalHolidayList.find((item) => item.date === date);
      return item?.changeFlag || false;
    },
  },
};
