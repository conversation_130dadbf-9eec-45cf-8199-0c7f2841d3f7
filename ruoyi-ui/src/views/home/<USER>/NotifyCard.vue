<template>
  <div v-bind='$attrs' :class='["workbench-notify-card",$attrs.class]'>
    <header class='workbench-notify-card-header'>
      <h4 class='workbench-notify-card__title'>{{title}}</h4>
      <div
        v-if="$slots.opera"
        class="opera-container"
      >
        <slot name="opera" />
      </div>
      <div
        v-if="$slots.headerContent"
        class="workbench-notify-card-header-content"
      >
        <slot name="headerContent" />
      </div>
    </header>
    <slot></slot>
  </div>
</template>

<script >
  export default {
    name: 'NotifyCard',
    props: {
      title: {
        type: String,
      }
    },
  }
</script >

<style lang='scss'>
  .workbench-notify-card{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: url("../assets/card-decorate.png") top right no-repeat;
    background-size: 214px 76px;
    background-color: #fff;
    border-radius: 8px;
    padding: 28px 20px 20px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;

    &-header{
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      row-gap: 18px;
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(#BFBFBF,.6);
      margin-bottom: 20px;
      .opera-container {
        display: flex;
        align-items: center;
      }
      &-content {
        width: 100%;
        display: flex;
        align-items: center;
        .workbench-tab {
          width: 100%;
          margin-bottom: -12px;
          // justify-content: space-between;
        }
      }
    }

    &__title{
      font-family: Source Han Sans CN Bold,serif;
      font-weight: bold;
      font-size: 18px;
      color: rgba(#333333,.95);
    }

    &__read{
      cursor: pointer;
      font-family: Source Han Sans CN,serif;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      opacity: 0.7;
      margin-left: 8px;
    }

    &__more{
      cursor: pointer;
      margin-left: auto;
      font-family: Source Han Sans CN,serif;
      font-weight: 400;
      font-size: 14px;
      color: #000;

      .el-icon-arrow-right{
        color: #000;
      }
    }
    &::before, &::after {
      content: '';
      width: 29px;
      height: 29px;
      background: url("../assets/card-horn-decorate.png") no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      bottom: 0;
    }
    &::after {
      left: initial;
      right: 0;
      transform: rotateZ(-90deg);
    }
  }
</style >
