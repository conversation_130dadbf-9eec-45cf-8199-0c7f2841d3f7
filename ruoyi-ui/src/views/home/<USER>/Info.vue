<template>
  <section class="workbench-info" :class="{ 'is-draggable': isDraggable }">
    <header class="workbench-info-header">
      <div class="workbench-info__title">
        <p>信息中心</p>
        <button class="workbench__button" @click="handlereliefOfAShift">
          交接班
        </button>
      </div>
      <ul class="workbench-tab">
        <li
          v-for="item in getTabList"
          :key="item.value"
          class="workbench-tab__item"
          :class="{
            'is-active': activeTab === item.value,
          }"
          @click="handleSwitchTab(item.value)"
        >
          {{ item.label }}
          <span v-if="item.count > 0">{{
            item.count > 99 ? 99 : item.count
          }}</span>
        </li>
      </ul>
    </header>
    <Empty class="workbench-info-main" :empty="isEmpty">
      <div
        v-for="(item, inx) in records"
        :key="`workbench-info-item-${activeTab}-${inx}`"
        :class="[`is-${item.type}`]"
        class="workbench-info-item"
      >
        <div class="workbench-info-item__icon" />
        <div class="workbench-info-item-content">
          <div class="workbench-info-item-header">
            <h4 class="workbench-info-item__title" :title="item.label">
              {{ item.label }}
            </h4>
            <span class="workbench-info-item__tag" :class="[`is-${item.type}`]">
              {{ getTag(item.type) }}
            </span>
          </div>
          <div
            v-if="item.type === 'todoList'"
            class="workbench-info-item__content"
            :title="item.content"
          >
            {{ item.content }}
          </div>
          <div class="workbench-info-item-info">
            <span class="workbench-info-item__date">{{ item.date }}</span>
            <template v-if="item.type === 'arrange'">
              <span class="workbench-info-item__line"></span>
              <span class="workbench-info-item__user">
                值班人：{{ item.userName }}
              </span>
            </template>
            <template v-if="item.type === 'exchange' && item.symbol">
              <span class="workbench-info-item__line"></span>
              <span class="workbench-info-item__user">
                股票代码：{{ item.symbol }}
              </span>
            </template>
          </div>

          <div v-if="item.type === 'todoList'" class="workbench-info-item-operates">
            <span
              class="workbench-info-item__operate"
              @click="handleDelEvent(item.id)"
            >
              <i class="el-icon-delete"></i>
            </span>
            <span
              class="workbench-info-item__operate"
              @click="handleOpenUpdateEvent(item)"
            >
              <i class="el-icon-edit"></i>
            </span>
          </div>
        </div>
      </div>

      <DutyLog
        v-if="activeTab === 'duty'"
        :handover-matters="infoData.handoverMatters"
        :special-matters="infoData.specialMatters"
      />
    </Empty>
    <el-dialog
      :visible="editEventState.visible"
      :append-to-body="true"
      title="编辑日程"
      width="30%"
      @close="handleEditEventClose"
    >
      <el-form
        ref="editEventForm"
        :model="editEventState.formData"
        :rules="editEventFormRules"
        label-width="80px"
      >
        <el-form-item prop="title" :label="editEventLabelMap.title">
          <el-input
            v-model="editEventState.formData.title"
            :maxlength="15"
            :show-word-limit="true"
            :placeholder="editEventLabelMap.title"
          />
        </el-form-item>
        <el-form-item prop="content" :label="editEventLabelMap.content">
          <el-input
            v-model="editEventState.formData.content"
            type="textarea"
            :rows="3"
            :maxlength="300"
            :show-word-limit="true"
            :placeholder="editEventLabelMap.content"
          />
        </el-form-item>
        <el-form-item prop="date" :label="editEventLabelMap.date">
          <el-date-picker
            v-model="editEventState.formData.date"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd HH:mm"
            style="width: 100%"
            popper-class="scale-picker-panel"
            :append-to-body="false"
            :default-time="['08:30:00', '17:30:00']"
          />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="handleEditEventClose">取消</el-button>
        <el-button
          :loading="editEventState.loading"
          type="primary"
          @click="handleUpdateEvent"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      :visible="reliefOfAShift.visible"
      :append-to-body="true"
      title="交接班"
      width="60%"
      @close="reliefOfAShift.visible = false"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form :model="reliefOfAShift.lastFormData" label-width="auto">
            <el-form-item :label="LastDayLabelMap.title">
              <el-select v-model="reliefOfAShift.lastFormData.shift">
                <el-option
                  v-for="item in reliefOfAShift.lastFormData.shifts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="LastDayLabelMap.user">
              <el-select v-model="reliefOfAShift.lastFormData.user">
                <el-option
                  v-for="item in reliefOfAShift.lastFormData.users"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="LastDayLabelMap.date">
              <el-input
                v-model="reliefOfAShift.lastFormData.changeDate"
                readonly
              />
            </el-form-item>
            <el-form-item :label="LastDayLabelMap.msg">
              <el-input
                type="textarea"
                :rows="5"
                placeholder="请输入内容"
                readonly
                v-model="reliefOfAShift.lastFormData.message"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <el-form :model="reliefOfAShift.formData" label-width="auto">
            <el-form-item :label="ToDayLabelMap.title">
              <el-select v-model="reliefOfAShift.formData.id">
                <el-option
                  v-for="item in reliefOfAShift.formData.shifts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="ToDayLabelMap.user">
              <el-input :value="userName" readonly />
            </el-form-item>
            <el-form-item :label="ToDayLabelMap.date">
              <el-input
                v-model="reliefOfAShift.formData.changeDate"
                readonly
                :placeholder="ToDayLabelMap.date"
              />
            </el-form-item>
            <el-form-item :label="ToDayLabelMap.message">
              <el-input
                type="textarea"
                :rows="5"
                placeholder="请输入内容"
                v-model="reliefOfAShift.formData.message"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="reliefOfAShift.visible = false">取消</el-button>
        <el-button
          :loading="editEventState.loading"
          type="primary"
          @click="saveReliefOfAShift"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </section>
</template>

<script>
import { sizeProp } from "@/views/home/<USER>";
import tabMixin from "@/views/home/<USER>/tab-mixin";
import dayjs from "dayjs";
import {
  addCalendar,
  deleteMemo,
  getChangeShift,
  saveChangeShift,
  updateCalendar,
} from "../../../api/workCalendar";
import DutyLog from "./DutyLog.vue";
import Empty from "./Empty.vue";

const tabList = [
  {
    label: "全部",
    value: "all",
  },
  {
    label: "日程信息",
    value: "todoList",
  },
  {
    label: "排班信息",
    value: "arrange",
  },
  // {
  //   label: "行政信息",
  //   value: "memo",
  // },
];

// 编辑时间标签映射
const editEventLabelMap = {
  title: "日程名称",
  content: "日程内容",
  date: "时间范围",
};

const ToDayLabelMap = {
  title: "今日交接班班次",
  user: "今日交接班人员",
  date: "今日交接班日期",
  message: "今日交接班内容",
};

const LastDayLabelMap = {
  title: "昨日交接班班次",
  user: "昨日交接班人员",
  date: "昨日交接班日期",
  msg: "昨日交接班内容",
};

export default {
  name: "Info",
  mixins: [tabMixin],
  components: {
    DutyLog,
    Empty,
  },
  inject: ["getCurrentDay", "getInfoData", "updateInfoData"],
  props: {
    isDraggable: {
      type: Boolean,
    },
    size: sizeProp,
  },
  data: () => {
    return {
      activeTab: tabList[0].value,
      editEventLabelMap,
      ToDayLabelMap,
      LastDayLabelMap,
      editEventFormRules: {
        title: [
          {
            required: true,
            message: `请输入${editEventLabelMap.title}`,
          },
        ],
        content: [
          {
            required: true,
            message: `请输入${editEventLabelMap.content}`,
          },
        ],
        date: [
          {
            required: true,
            message: `请选择${editEventLabelMap.date}`,
          },
        ],
      },
      editEventState: {
        visible: false,
        loading: false,
        formData: {
          id: "",
          title: "",
          content: "",
          date: [],
        },
      },
      reliefOfAShift: {
        visible: false,
        loading: false,
        lastFormData: {
          shift: "",
          user: "",
          changeDate: "",
          message: "",
        },
        formData: {
          id: "",
          message: "",
          changeDate: "",
        },
      },
    };
  },
  computed: {
    userName() {
      return this.$store.state.user.name;
    },
    getTabList() {
      return tabList.map((item) => {
        return {
          ...item,
          count:
            item.value === "all"
              ? this.allInfoData.length
              : this.infoData[item.value].length,
        };
      });
    },
    infoData() {
      return this.getInfoData();
    },
    allInfoData() {
      const data = this.infoData;
      return [...data.arrange, ...data.todoList];
    },
    records() {
      const data = this.infoData;

      switch (this.activeTab) {
        case "all":
          return this.allInfoData;
        case "arrange":
        case "todoList":
          return data[this.activeTab];
        default:
          return [];
      }
    },
    isEmpty() {
      if (this.activeTab === "duty") {
        return (
          !this.records.length &&
          !this.infoData.specialMatters.length &&
          !this.infoData.handoverMatters.length
        );
      } else {
        return !this.records.length;
      }
    },
  },
  watch: {
    "reliefOfAShift.lastFormData.shift"(value) {
      this.setMsgDate(this.reliefOfAShift.lastFormData.user, value);
    },
    "reliefOfAShift.lastFormData.user"(value) {
      this.setMsgDate(value, this.reliefOfAShift.lastFormData.shift);
    },
    "reliefOfAShift.formData.id"(value) {
      this.setMsgDate2(value);
    },
  },
  methods: {
    // 获取标签
    getTag(type) {
      return tabList.find((item) => item.value === type)?.label;
    },
    async handlereliefOfAShift() {
      this.reliefOfAShiftClose();
      const data = await getChangeShift(this.getCurrentDay());
      const { lastDay, today } = data.data;
      this.reliefOfAShift.lastFormData.lastDays = (
        Object.values(lastDay) || []
      ).flat();

      this.reliefOfAShift.formData.toDays = (Object.values(today) || []).flat();

      this.reliefOfAShift.lastFormData.shifts = (
        Object.keys(lastDay) || []
      ).map((x) => ({ label: x, value: x }));

      this.reliefOfAShift.lastFormData.shift = Object.keys(lastDay)?.[0] || "";

      const map = (Object.values(lastDay) || []).flat().reduce((result, x) => {
        if (!result.has(x.userId)) {
          result.set(x.userId, { label: x.userName, value: x.userId });
        }
        return result;
      }, new Map());

      this.reliefOfAShift.lastFormData.users = [...map.values()];
      this.reliefOfAShift.lastFormData.user = [...map.values()]?.[0]?.value;

      const map2 = (Object.values(today) || []).flat().reduce((result, x) => {
        if (!result.has(x.userId)) {
          result.set(x.id, { label: x.shiftName, value: x.id });
        }
        return result;
      }, new Map());
      this.reliefOfAShift.formData.shifts = [...map2.values()];
      this.reliefOfAShift.formData.id = [...map2.values()]?.[0]?.value;

      this.reliefOfAShift.visible = true;
    },
    async saveReliefOfAShift() {
      await saveChangeShift({
        id: this.reliefOfAShift.formData.id,
        message: this.reliefOfAShift.formData.message,
      });
      this.$message.success("交接成功！");
      this.reliefOfAShift.visible = false;
    },
    reliefOfAShiftClose() {
      this.reliefOfAShift.formData.id = "";
      this.reliefOfAShift.formData.message = "";
      this.reliefOfAShift.formData.changeDate = "";
      this.reliefOfAShift.lastFormData.shift = "";
      this.reliefOfAShift.lastFormData.message = "";
      this.reliefOfAShift.lastFormData.changeDate = "";
      this.reliefOfAShift.lastFormData.user = "";
    },
    setMsgDate(user, shift) {
      const has = this.reliefOfAShift.lastFormData.lastDays.find(
        (x) => x.shiftName === shift && x.userId === user
      );
      this.reliefOfAShift.lastFormData.message = has?.message || "无";
      this.reliefOfAShift.lastFormData.changeDate = has?.changeDate || "无";
    },
    setMsgDate2(id) {
      const has = this.reliefOfAShift.formData.toDays.find((x) => x.id === id);

      this.reliefOfAShift.formData.message = has.message;
      this.reliefOfAShift.formData.changeDate = has.changeDate;
    },
    handleDelEvent(id) {
      this.$confirm("删除日程信息, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteMemo(id).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.updateInfoData();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleOpenUpdateEvent(item) {
      // const dateList = item.date.split("~");
      this.editEventState.formData = {
        id: item.id,
        title: item.taskName,
        content: item.taskDesc,
        date: [item.taskStartTime, item.taskEndTime],
      };
      this.editEventState.visible = true;
    },
    handleUpdateEvent() {
      this.$refs["editEventForm"].validate((valid) => {
        if (valid) {
          this.editEventState.loading = true;
          // const date = this.editEventState.formData.date;
          // const params = {
          //   // id: this.editEventState.formData.id,
          //   // priority: "2",
          //   // title: this.editEventState.formData.title,
          //   // content: this.editEventState.formData.content,
          //   // date: this.getCurrentDay(),
          //   // startTime: dayjs(date[0]).format("YYYY-MM-DD HH:mm:ss"),
          //   // endTime: dayjs(date[1]).format("YYYY-MM-DD HH:mm:ss"),
          //   // handler: [],
          // };
          const params = {
            id: this.editEventState.formData.id,
            taskName: this.editEventState.formData.title,
            taskDesc: this.editEventState.formData.content,
            taskStartTime: dayjs(this.editEventState.formData.date[0]).format('YYYY-MM-DD HH:mm:ss'),
            taskEndTime: dayjs(this.editEventState.formData.date[1]).format('YYYY-MM-DD HH:mm:ss')
          }
          updateCalendar(params)
            .then((res) => {
              if (res.code === 200) {
                this.$message.success("修改成功");
                this.handleEditEventClose();
                this.updateInfoData();
              } else {
                this.$message.error("修改失败");
              }
            })
            .finally(() => {
              this.editEventState.loading = false;
            });
        } else {
          return false;
        }
      });
    },
    handleEditEventClose() {
      this.editEventState.visible = false;
    },
  },
};
</script>

<style lang="scss" src="../styles/info.scss"></style>
