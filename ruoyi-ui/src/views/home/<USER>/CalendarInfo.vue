<template>
  <div ref='root' class='workbench-calendarinfo' :class='[`is-${size}`]'>
    <Calendar :is-draggable='isDraggable'/>
    <Info :is-draggable='isDraggable'/>
  </div>
</template>

<script>
import Calendar from "./Calendar.vue";
import Info from "./Info.vue";
export default {
  name: 'CalendarInfo',
  components: {
    Calendar,
    Info
  },
  props:{
    isDraggable:{
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      size: 'normal'
    }
  },
  mounted() {
    const resizeOb = new ResizeObserver(this.watchResize.bind(this))
    resizeOb.observe(this.$refs.root)
    this.resizeOb = resizeOb
  },
  beforeDestroy() {
    this.destoryOb()
  },
  methods: {
    destoryOb(){
      if (this.resizeOb){
        this.resizeOb.disconnect()
      }
    },
    watchResize([entire]){
      if (entire.contentRect.height >= 510){
        this.size = 'normal'
      } else {
        this.size = 'small'
      }
    }
  }
}
</script>

<style lang='scss'>
  .workbench-calendarinfo{
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    .workbench-calendar{
      flex: auto;
      max-height: 316px;
      border-bottom-left-radius: unset;
      border-bottom-right-radius: unset;
      &::before, &::after {
        display: none;
      }
    }

    .workbench-info{
      flex: auto;
      padding-top: 2px;
      max-height: 210px;
      border-top-left-radius: unset;
      border-top-right-radius: unset;
      background-image: unset;
    }

    &.is-small{
      .workbench-calendar{
        min-height: 276px;
        max-height: 276px;
      }
    }
  }
</style>
