$primary-color: #165dff;

// ================== Base START

// ================== Base END

.home.is-blue {
  // ================== Base START

  .workbench-wrapper {
    background-color: transparent;
  }

  .resizeable-remove {
    color: #77a0ff;
  }

  .workbench-operate-button {
    color: $primary-color;
  }
  .workbench-operate-drawer__collapse {
    background-color: $primary-color;
  }
  .workbench__button {
    color: #fff;
    border: none;
    background-color: #165dff;

    &.is-outline {
      border-radius: 4px;
      background-color: #f2f5f7;
      color: #96a9bf;
    }
  }

  .workbench-tab__item {
    color: #96a9bf;
  }
  .workbench-tab__item.is-active {
    color: #333;
  }
  .workbench-tab__item.is-active::before {
    background-color: #0444e1;
  }
  // ================== Base END

  // ================== Calendar START
  .workbench-calendar {
    background-image: unset;
    &::before,
    &::after {
      display: none;
    }
  }

  .workbench-calendar-tab {
    background-color: #f2f5f7;
  }
  .workbench-calendar-tab__item {
    color: #abbbcc;

    &.is-active {
      color: #fff;
      background-color: $primary-color;
    }
  }

  .workbench-calendar-tags__item {
    background-image: unset;
    border-radius: 4px;
    &.is-a {
      background: rgba(167, 201, 255, 0.5);
    }
    &.is-etf {
      background: linear-gradient(
        90deg,
        rgba(149, 249, 231, 0.5) 0%,
        rgba(195, 192, 255, 0.5) 0%,
        rgba(138, 248, 221, 0.5) 0%
      );
    }
    &.is-amer {
      background: rgba(255, 206, 205, 0.5);
    }
  }

  .workbench-calendar-main {
    background-color: #f7f8f8;
  }

  .workbench-calendar__col {
    &.is-today > span {
      color: #124ee2;
      background: #ebf1fe;
      border-radius: 5px;
      border: 2px solid #0444e1;
    }
  }
  .workbench-calendar-event {
    &-item {
      &.is-today {
        background-color: rgba(147, 141, 255, 0.15);
        border-color: #2f47ff;

        .workbench-calendar-event__day,
        .workbench-calendar-event__week {
          color: #0048ff;
        }
      }
    }
  }
  .workbench-calendar__event {
    &:focus,
    &:hover {
      color: #124ee2;
    }
  }
  // ================= Calendar END

  // ================== Info START
  .workbench-info {
    background-image: unset;
    .workbench-info-item {
      background-color: rgba(#8f8ab5, 0.09);
    }
    &::before,
    &::after {
      display: none;
    }
  }
  .workbench-info-item-operates {
    background-color: rgba($primary-color, 0.7);
  }
  // ================== Info END

  // ================== Work START
  .workbench-work {
    background-image: unset;
    &::before,
    &::after {
      display: none;
    }
  }

  .workbench-work .workbench-tab__item.is-active {
    color: #0444e1;
  }
  .workbench-work .custom-table thead tr th {
    background-color: #f7f8f8;
  }
  .workbench-work .child-table tbody tr.is-active td:first-child,
  .workbench-work .child-table tbody tr.current-row td:first-child {
    border-left-color: #0444e1;
  }
  .workbench-work .child-table tbody tr.is-active td .cell,
  .workbench-work .child-table tbody tr.current-row td .cell {
    color: #0444e1 !important;
    .el-radio .el-radio__label {
      color: #0444e1;
    }
  }
  .workbench-work .child-table tbody tr.is-active td,
  .workbench-work .child-table tbody tr.current-row td {
    background-color: #ebf1fe !important;
  }
  .workbench-work .child-table thead th th,
  .workbench-work .child-table tbody tr td {
    background-color: #f7f8f8;
  }

  .workbench-work .custom-table .el-radio__input.is-checked .el-radio__inner {
    border-color: #0444e1;
    background-color: #0444e1;
  }
  .el-table__expand-icon .el-icon-arrow-right::before {
    background-image: url("../assets/blue/table-expand.png");
  }
  .el-table__expand-icon.el-table__expand-icon--expanded
    .el-icon-arrow-right::before {
    background-image: url("../assets/blue/table-open.png");
  }

  .el-radio-button__inner:hover {
    color: #1890ff;
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: #fff;
    background-color: #165dff;
    border-color: #165dff;
    box-shadow: -1px 0 0 0 #165dff;
  }

  // ================== Work END

  // ================== Link START
  .workbench-link {
    &::before,
    &::after {
      display: none;
    }
    &-item {
      &__close {
        color: #0444e1;
      }
    }
  }
  // ================== Link END

  // ================== About START
  .workbench-about {
    &-inner {
      &::after {
        display: none;
      }
    }
    &-content {
      padding: 12px 12px 0 14px;
      background: url("../assets/blue/about-blue-bg.png") no-repeat;
      background-size: 100% 100%;
      background-color: unset;
    }
    &-main {
      margin-bottom: 20px;
    }
    &-header {
      padding-bottom: 12px;
    }
    &__title {
      font-size: 24px;
      letter-spacing: 2px;
      color: #0444e1;
      text-align: left;
    }
    &__record {
      background: rgba(#000000, 0.2);
    }
    &__desc {
      color: #202225;
    }
    &-time {
      &-item {
        span {
          &:nth-child(2) {
            background-color: #2173f5;
          }
          &:not(:nth-child(2)) {
            color: #202225;
          }
        }
      }
    }
  }
  // ================== About END

  .workbench-notify-card {
    background-image: unset;
    &::before,
    &::after {
      display: none;
    }
  }

  // ================== Flow START

  // ================== Flow END

  // ================== Monitor START

  // ================== Monitor END

  // ================== Timeline START
  .workbench-timeline-item-info-content {
    background-image: linear-gradient(
      to right,
      rgba(#e3f0ff, 0.7),
      transparent
    );
  }
  .workbench-timeline-item__order {
    background-color: rgba(#e3f0ff, 0.6);
  }
  .workbench-timeline-item__detail {
    color: #999;
  }
  // ================== Timeline END

  // ================== Empty START
  .workbench-empty.is-empty {
    .workbench-empty__text {
      background-image: url("../assets/blue/empty-bg.png");
      color: rgba($primary-color, 0.6);
    }
  }
  // ================== Empty END
}
