import {
  generateCalendar,
  generateCalendarWithRestDays,
} from "@/views/home/<USER>/calendar-helper";
import dayjs from "dayjs";
import { getCalendar, getCalendarHolidays } from "@/api/workCalendar";
import calendarBaseMixin from "./calendar-base-mixin";

const calendarType = [
  {
    label: "月",
    value: "month",
  },
  {
    label: "周",
    value: "week",
  },
];

const currentDay = dayjs();

const calendars = generateCalendarWithRestDays(currentDay);

export default {
  inject: ["getCurrentDay", "changeCurrentDate"],
  mixins: [calendarBaseMixin],
  data: () => {
    return {
      size: "normal",
      calendarType,
      calendarActiveType: calendarType[0].value,
      weekDays: calendars.weekDays,
      calendar: calendars.calendar,
      holidayList: [],
      internalHolidayList: [],
      eventList: [],
    };
  },
  methods: {
    getData() {
      const lastWeek = this.calendar[this.calendar.length - 1];
      const params = [
        this.calendar[0][0].fullDate,
        lastWeek[lastWeek.length - 1].fullDate,
      ];

      getCalendar(params[0], params[1]).then((res) => {
        if (res.code.toString() === "200") {
          this.holidayList = res.data.todoList;
          this.eventList = res.data.arrange;
        }
      });
      getCalendarHolidays(params[0], params[1]).then((res) => {
        if (res.code.toString() === "200") {
          this.internalHolidayList = res.data;
        }
      });
    },
    nextMonth() {
      const updateDate = dayjs(this.currentDay).subtract(-1, "month");
      this.changeCurrentDate(updateDate);
      this.changeCalendarBySize(this.size, updateDate);
    },
    prevMonth() {
      const updateDate = dayjs(this.currentDay).subtract(1, "month");
      this.changeCurrentDate(updateDate);
      this.changeCalendarBySize(this.size, updateDate);
    },
    changeDate(data) {
      const updateDate = data ? dayjs(data.fullDate) : dayjs();
      this.changeCurrentDate(updateDate);
      this.changeCalendarBySize(this.size, updateDate);
    },
    onChangecalendarActiveType(value) {
      let calendar;
      this.calendarActiveType = value;
      if (value === "month") {
        const calendars = generateCalendarWithRestDays(activeDay);
        calendar = calendars.calendar;
      }
      if (value === "week") {
        const calendars = generateCalendar(
          activeDay,
          val === "small" ? 1 : 2,
          activeDay
        );
        calendar = calendars.calendar;
      }
      this.calendar = calendar;
      this.getData();
    },
    changeCalendarBySize(val, activeDay) {
      let calendar;
      if (val === "normal") {
        const calendars = generateCalendarWithRestDays(activeDay);
        calendar = calendars.calendar;
        this.calendarActiveType = "month";
      } else {
        const calendars = generateCalendar(
          activeDay,
          val === "small" ? 1 : 2,
          activeDay
        );
        calendar = calendars.calendar;
        this.calendarActiveType = "week";
      }
      this.calendar = calendar;
      this.getData();
    },
  },
  computed: {
    displayMonth() {
      if (this.currentDay) {
        return dayjs(this.currentDay).format("YYYY年M月");
      }
      return dayjs().format("YYYY年M月");
    },
    currentDay() {
      return this.getCurrentDay();
    },
    currentDayEvents() {
      return [...this.eventList].filter((x) => x.date === this.currentDay);
    },
  },
  watch: {
    size(val) {
      this.changeCalendarBySize(val, dayjs(this.currentDay));
    },
  },
};
