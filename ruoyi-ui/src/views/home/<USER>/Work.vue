<template>
  <section ref="root" class="workbench-work" :class="[`is-${size}`]">
    <header class="workbench-work-header">
      <h3 class="workbench-work__title">待办工作</h3>
    </header>
    <main class="workbench-work-main">
      <div class="workbench-work-search">
        <el-radio-group v-model="templateKey" size="mini">
          <el-radio-button v-for="item in templateKeys" :label="item" :key="item">{{ item }}</el-radio-button>
        </el-radio-group>
      </div>
      <el-table ref="table" v-loading="loading" row-key="id" size="small" :tree-props="{ children: 'children' }"
        class="custom-table" :data="records" :row-class-name="rowClassName">
        <!-- <el-table-column type="selection" width="48" /> -->
        <el-table-column label="序号" width="160" prop="taskNo">
          <template #default="{ row }">
            {{ row.taskNo }}
          </template>
        </el-table-column>
        <el-table-column prop="taskName" show-overflow-tooltip label="业务名称" />
        <el-table-column prop="taskCompleteDesc" show-overflow-tooltip label="备注" />
        <el-table-column prop="taskCompleteStatus" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="stateMap[scope.row.taskCompleteStatus].color">
              {{ stateMap[scope.row.taskCompleteStatus].label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="scope.row.taskCompleteStatus !== '3'" size="mini" type="text" icon="el-icon-circle-check"
              @click="handleComplete(scope.row, scope.row.id)" />
            <el-dropdown size="mini" @command="($event) => handleCommand($event, scope.row)">
              <el-button size="mini" type="text" icon="el-icon-more" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="['0', '1'].includes(scope.row.taskCompleteStatus)" command="unrealizedModalRef">
                  未发生
                </el-dropdown-item>
                <el-dropdown-item command="transferModalRef">
                  转派
                </el-dropdown-item>
                <el-dropdown-item command="fileListModalRef">
                  上传附件
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <CompleteModal ref="completeModalRef" @success="getData" />
    <transfer-modal ref="transferModalRef" @success="getData" />
    <unrealized-modal ref="unrealizedModalRef" @success="getData" />
    <file-list-modal ref="fileListModalRef" />
    <!-- 工作代办弹窗 -->
    <el-dialog title="工作代办" :visible.sync="createWorkState.visible" :append-to-body="true" width="30%">
      <el-form ref="form" :model="createWorkState.formData" label-width="120px">
        <el-form-item class="work-form-input" label="工作代办人:" prop="handoverId"
          :rules="[{ required: true, message: '工作代办人不能为空' }]">
          <el-select v-model="createWorkState.formData.handoverId" placeholder="请选择工作代办人">
            <el-option v-for="item in options.operates" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="工作代办内容:" prop="menuIds" :rules="[{ required: true, message: '工作代办内容不能为空' }]">
          <el-select v-model="createWorkState.formData.menuIds" multiple collapse-tags placeholder="请选择">
            <el-option v-for="item in options.menus" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-show="createWorkState.formData.menuIds.includes('20001')" label="工作明细:" prop="detailIds">
          <el-select v-model="createWorkState.formData.detailIds" multiple collapse-tags placeholder="请选择">
            <el-option v-for="(item, index) in options.menuDetailList" :key="index"
              :class="item.turn === '1' ? 'blue' : ''" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间:" prop="startTime" :rules="[{ required: true, message: '开始时间不能为空' }]">
          <el-date-picker v-model="createWorkState.formData.startTime" popper-class="hide-time  scale-picker-panel"
            style="width: 100%" type="datetime" placeholder="选择日期时间" @change="startTimeChange" />
        </el-form-item>
        <el-form-item label="结束时间:" prop="endTime" :rules="[{ required: true, message: '结束时间不能为空' }]">
          <el-date-picker popper-class="hide-time scale-picker-panel" style="width: 100%" :append-to-body="false"
            v-model="createWorkState.formData.endTime" type="datetime" placeholder="选择日期时间" @change="endTimeChange">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
          <el-button @click="handleClose">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </section>
</template>

<script>
import tabMixin from "@/views/home/<USER>/tab-mixin";
import dayjs from "dayjs";
import { setTodo, setWorkHandover, getTodoList } from "@/api/todoWork";
import { getTaskTemplateList } from "@/api/task-template";
import { hasPermission2D } from "@/utils/index";
import Empty from "./Empty.vue";
import WorkSuccessResult from "./WorkSuccessResult.vue";
import CompleteModal from "../../task-center/task-list/components/WorkbenchCompleteModal.vue";
import TransferModal from "../../task-center/task-list/components/TransferModal.vue";
import FileListModal from "../../task-center/task-list/components/FileListModal.vue";
import UnrealizedModal from "../../task-center/task-list/components/UnrealizedModal.vue";

const stateMap = {
  0: { label: "未开始", color: "info" },
  1: { label: "进行中", color: "warning" },
  2: { label: "待复核", color: "" },
  3: { label: "已完成", color: "success" },
  5: { label: "未发生", color: "info" },
};

export default {
  name: "Work",
  mixins: [tabMixin],
  components: {
    Empty,
    WorkSuccessResult,
    CompleteModal,
    TransferModal,
    FileListModal,
    UnrealizedModal,
  },
  inject: ["getCurrentDay"],
  props: {
    isDraggable: {
      type: Boolean,
    },
  },
  data: () => {
    return {
      stateMap,
      recordsMap: new Map(),
      templateKey: "",
      size: "normal",
      loading: false,
      showWorkManage: true,
      options: {
        operates: [],
        menus: [],
        menuDetailList: [],
        workList: [],
        templateList: [],
      },
      createWorkState: {
        visible: false,
        loading: false,
        formData: {
          handoverId: "",
          menuIds: [],
          detailIds: [],
          startTime: "",
          endTime: "",
        },
      },
      queryParams: {
        charger: "",
        toDoWork: [],
        title: "member",
        date: [dayjs(), dayjs()],
      },
    };
  },
  async mounted() {
    const resizeOb = new ResizeObserver(this.watchResize.bind(this));
    resizeOb.observe(this.$refs.root);
    this.resizeOb = resizeOb;

    this.showWorkManage = hasPermission2D(
      this.$store,
      "operateMonitor",
      "WorkManage"
    );
    await this.getOptions();
    this.getData();
  },
  beforeDestroy() {
    this.destoryOb();
  },
  computed: {
    isSmall({ size }) {
      return size === "small";
    },
    isNormal({ size }) {
      return size === "normal";
    },
    isUndo() {
      return this.activeTab === "undo";
    },
    params() {
      const params = {
        ...this.queryParams,
        toDoWork: this.queryParams.toDoWork.join(","),
        startDate: dayjs(this.queryParams.date[0]).format("YYYY-MM-DD"),
        endDate: dayjs(this.queryParams.date[1]).format("YYYY-MM-DD"),
        complete: this.isUndo ? 0 : 1,
      };
      if (this.isUndo) {
        return {
          ...params,
          charger: "",
          toDoWork: "",
          startDate: "",
          endDate: "",
        };
      }
      return params;
    },
    currentDay() {
      return this.getCurrentDay();
    },
    templateKeys() {
      return [...this.recordsMap.keys()];
    },
    records() {
      let r = this.recordsMap.get(this.templateKey);
      function insertNo(list, prefix) {
        for (let i = 0; i < list.length; i++) {
          list[i].taskNo = `${prefix ? prefix + '-' : ''}${i + 1}`
          if (list[i].children && list[i].children.length > 0) {
            insertNo(list[i].children, i + 1)
          }
        }
      }
      if (r) insertNo(r)
      return r || [];
    },
  },
  watch: {
    currentDay(v) {
      this.getData();
    },
  },
  methods: {
    async getOptions() {
      this.options.templateList = (await getTaskTemplateList()).data;

      // const [operators, menus, tasks] = await Promise.all([
      //   getOperators(),
      //   getMenu(),
      //   getTasks(),
      // ]);

      // const options = {};

      // if (operators.status.toString() === "200") {
      //   options.operates = operators.data.map((item) => ({
      //     label: item.userName,
      //     value: item.userCode,
      //   }));
      // }

      // if (menus.status.toString() === "200") {
      //   options.menus = menus.data.map((item) => {
      //     if (item.id === "20001") {
      //       options.menuDetailList = item.children.map((child) => ({
      //         label: child.templateName,
      //         value: child.id,
      //         turn: child.turn,
      //       }));
      //     }

      //     return {
      //       label: item.title,
      //       value: item.id,
      //     };
      //   });
      // }

      // if (tasks.code.toString() === "200") {
      //   options.workList = tasks.result.map((item) => ({
      //     label: item.label,
      //     value: item.label,
      //   }));
      // }

      // this.options = options;
    },
    async getData(params) {
      this.loading = true;
      getTodoList({
        date: this.currentDay,
        // orgId: this.$store.state.user.user.deptId,
      })
        .then((res) => {
          if (res.code === 200) {
            this.recordsMap = res.data.reduce((result, item) => {
              const key =
                this.options.templateList.find(
                  (x) => x.id === item.taskBindTemplateId
                )?.templateName || "其他";
              if (result.has(key)) {
                result.set(key, [...result.get(key), item]);
              } else {
                result.set(key, [item]);
              }
              return result;
            }, new Map());
            this.templateKey = [...this.recordsMap.keys()][0];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleComplete(record, rootId) {
      this.$refs.completeModalRef.openModal(record, rootId);
    },
    getRootId(id) {
      const flatOptions = [];
      const flatList = (options) => {
        options.forEach((option) => {
          const item = cloneDeep(option);
          item.children = [];
          flatOptions.push(item);
          if (option.children && option.children.length > 0) {
            flatList(option.children);
          }
        });
      };
      flatList(this.getData);
      const found = flatOptions.find(
        (el) =>
          el.parentId === "0" &&
          el.taskChildIds &&
          el.taskChildIds.split(",").includes(id)
      );
      return found ? found.id : null;
    },
    handleCommand(command, data) {
      console.log("🚀 ~ handleCommand ~ command:", command);
      switch (command) {
        case "fileListModalRef":
          this.$refs[command].openModal(data.id);
          break;
        default:
          this.$refs[command].openModal(data);
          break;
      }
    },
    handleSearch() {
      this.getData();
    },
    toWorkManage() {
      // 跳转到工作管理
      this.parentRouter?.push({ name: "WorkManage" });
    },
    destoryOb() {
      if (this.resizeOb) {
        this.resizeOb.disconnect();
      }
    },
    watchResize([entire]) {
      if (entire.contentRect.width >= 850) {
        this.size = "normal";
      } else if (entire.contentRect.width >= 700) {
        this.size = "middle";
      } else {
        this.size = "small";
      }
    },
    handleSwitchChange() {
      this.getData();
    },
    startTimeChange() {
      if (
        dayjs(this.workForm.endTime).isBefore(dayjs(this.workForm.startTime))
      ) {
        this.$message.error("开始时间不能大于结束时间");
        this.workForm.startTime = "";
      }
    },
    endTimeChange() {
      if (
        dayjs(this.workForm.endTime).isBefore(dayjs(this.workForm.startTime))
      ) {
        this.$message.error("结束时间不能小于开始时间");
        this.workForm.endTime = "";
      }
    },
    handleClose() {
      this.createWorkState = {
        visible: false,
        loading: false,
        formData: {
          handoverId: "",
          menuIds: [],
          detailIds: [],
          startTime: "",
          endTime: "",
        },
      };
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return false;
        }
        const formData = this.createWorkState.formData;
        formData.startTime = dayjs(formData.startTime).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        formData.endTime = dayjs(formData.endTime).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        setWorkHandover(formData).then((res) => {
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.handleClose();
            if (this.isUndo) {
              this.getData();
            }
          } else {
            this.$message.error(res.message);
          }
        });
      });
    },
    handleOperateByParent(row, state) {
      if (
        dayjs().valueOf() < dayjs(`${row.startDate} ${row.startTime}`).valueOf()
      ) {
        this.$message.warning("待办工作尚未开始");
        return;
      }
      this.$confirm("确认操作？").then(() => {
        const { id, steps } = row;
        if (steps && steps.length) {
          this.handleChangeTaskState({
            id,
            signs: steps
              .map((item) => {
                if (item.completeState === null || item.completeState === "2") {
                  return {
                    stepId: item.stepId,
                    state,
                  };
                }
                return null;
              })
              .filter((item) => item),
          });
        } else {
          this.handleChangeTaskState({
            id,
            state,
          });
        }
      });
    },
    handleOperateInStep(id, row, state) {
      this.$confirm("确认操作？").then(() => {
        this.handleChangeTaskState({
          id,
          signs: [
            {
              stepId: row.stepId,
              state,
            },
          ],
        });
      });
    },
    handleChangeTaskState(params) {
      setTodo(params).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getData();
        } else {
          this.$message.error("操作失败");
        }
      });
    },

    handleRowClick(row) {
      console.log(row);
    },
    formatDate(date) {
      if (!date) {
        return "";
      }
      return dayjs(date).format("YYYY-MM-DD HH:mm");
    },

    rowClassName({ row }) {
      if (this.activeTab !== "undo") {
        return "";
      }
      let endDatetime = dayjs(`${row.endDate} ${row.endTime}`);
      let currentTime = dayjs();
      if (currentTime.isAfter(endDatetime)) {
        return "is-error";
      }
      if (endDatetime.subtract(30, "minute").isBefore(currentTime)) {
        return "is-warn";
      }
      return "";
    },
    getComposeTime(row, keys) {
      return `${dayjs(row[keys[0]]).format("MM-DD")} ${row[keys[1]]}`;
    },
  },
};
</script>

<style lang="scss" src="../styles/work.scss"></style>
