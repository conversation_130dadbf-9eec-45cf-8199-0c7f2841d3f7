<template>
  <div class="duty-log">
    <section
      v-show="handoverMatters.length > 0"
      class='duty-log-item'
    >
      <header class='duty-log-item-header'>
        <span class='duty-log-item__icon is-transfer'></span>
        <h4 class='duty-log-item__title'>交接事项</h4>
        <span class='duty-log-item__line'></span>
      </header>
      <ul class='duty-log-list'>
        <li
          v-for='(item,index) in handoverMatters'
          :key='`duty-log-list-transfer-${index}`'
          class='duty-log-list-item is-transfer'
        >
          {{item}}
        </li>
      </ul>
    </section>

    <section
      v-show="specialMatters.length > 0"
      class='duty-log-item'
    >
      <header class='duty-log-item-header'>
        <span class='duty-log-item__icon'></span>
        <h4 class='duty-log-item__title'>特殊事项</h4>
        <span class='duty-log-item__line'></span>
      </header>
      <ul class='duty-log-list'>
        <li
          v-for='(item,index) in specialMatters'
          :key='`duty-log-list-special-${index}`'
          class='duty-log-list-item'
        >
          {{item}}
        </li>
      </ul>
    </section>
  </div>
</template>

<script>
export default {
  name: 'DutyLog',
  props: {
    width: {
      type: String,
      default: '100%',
    },
    specialMatters: {
      type: Array,
      require: true
    },
    handoverMatters: {
      type: Array,
      require: true
    },
  },
  data () {
    return {
      img1: require('../assets/transfer-event-icon.png'),
      img2: require('../assets/special-event-icon.png'),
      activeNames: ['1', '2']
    }
  },
}
</script>

<style scoped lang="scss">
.duty-log {
  width: 100%;

  &-item{
    margin-top: 20px;

    &-header{
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

    &__icon{
      width: 21px;
      height: 19px;
      background-image: url("../assets/special-event-icon.png");
      background-repeat: no-repeat;
      background-size: cover;
      display: inline-block;
      margin-right: 6px;

      &.is-transfer{
        background-image: url("../assets/transfer-event-icon.png");
      }
    }

    &__title{
      font-family: Source Han Sans CN Bold,serif;
      font-weight: bold;
      font-size: 16px;
      color: #202225;
      display: block;
    }

    &__line{
      width: calc(100% - 100px);
      margin-left: auto;
      height: 1px;
      background-color: rgba(#202225,.08);
    }
  }

  &-list{
    display: flex;
    flex-direction: column;
    row-gap: 6px;

    &-item{
      font-family: Source Han Sans CN,serif;
      font-weight: 400;
      font-size: 14px;
      color: rgba(#666666,.8);
      padding-left: 16px;
      position: relative;

      &::before{
        position: absolute;
        content: "";
        display: block;
        top: calc(50% - 3px);
        left: 0;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #FF5A5A;
      }

      &.is-transfer::before{
        background-color: #4D6AFF;
      }
    }
  }
}
</style>
