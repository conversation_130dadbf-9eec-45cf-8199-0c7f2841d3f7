<template>
  <NotifyCard
    class='workbench-flow'
    title='监控中心'
  >
    <template slot="opera">
      <div class="workbench-notify-card__more" @click="toMonitors">
        查看更多
        <i class="el-icon-arrow-right"></i>
      </div>
    </template>
    <Empty
      class='workbench-timeline'
      :class='{
        "is-draggable": isDraggable
      }'
      :empty='!monitorList.length'
    >
      <div
        v-for='item in monitorList'
        :key='`workbench-monitor-item-${item.indicatorName}`'
        class='workbench-timeline-item workbench-timeline-monitor-item'
      >
        <div class='workbench-timeline-item__dot is-success'></div>
        <div class='workbench-timeline-item-content'>
          <div class='workbench-timeline-item-info-content'>
            <div :class="['workbench-timeline-item__tag', item.monitorStatus === '1' ? 'fail' : 'warning']">{{getTime(item.sendInfo)}}</div>
            <div :title="item.indicatorName" class='workbench-timeline-item__name'>{{item.indicatorName}}</div>
            <div :class="['workbench-timeline-item__status', item.monitorStatus === '1' ? 'fail' : 'warning']"></div>
            <div class='workbench-timeline-item__detail' @click='toMonitorDetail'>详情</div>
          </div>
        </div>
      </div>
    </Empty>
  </NotifyCard>
</template>

<script >
  import NotifyCard from "@/views/home/<USER>/NotifyCard.vue";
  import Empty from "./Empty.vue";
  import dayjs from 'dayjs'

  export default {
    name: 'Monitor',
    inject: ['getMonitorList', 'toMonitors', 'toMonitorDetail'],
    props:{
      isDraggable: {
        type: Boolean
      }
    },
    components: {
      NotifyCard,
      Empty
    },
    computed: {
      monitorList(){
        return this.getMonitorList()
      },
      getTime() {
        return (value) => {
          const result = dayjs(value)
          return result.isValid() ? result.format('HH:mm') : value
        }
      }
    }
  }
</script >
