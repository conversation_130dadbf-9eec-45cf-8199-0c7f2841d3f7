.workbench-info{
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px;
  background: url("../assets/card-decorate.png") top right no-repeat;
  background-size: 214px 76px;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  position: relative;

  &.is-draggable{
    .workbench-info-main{
      overflow: hidden;
    }
  }

  &-header{
    border-bottom: 1px solid rgba(#EEEEEE,.7);
    padding-bottom: 8px;
  }

  &__title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Source Han <PERSON>s <PERSON>N Bold,serif;
    font-weight: bold;
    font-size: 18px;
    color: rgba(#333333,.95);
    margin-bottom: 16px;
  }

  &__more{
    cursor: pointer;
    margin-left: auto;
    font-family: Source Han <PERSON>,serif;
    font-weight: 400;
    font-size: 14px;
    color: #333333;

    .el-icon-arrow-right{
      color: #686868;
    }
  }

  &-main{
    flex: 1;
    margin-top: 16px;
    display: flex;
    padding-right: 4px;
    flex-direction: column;
    row-gap: 8px;
    overflow-y: auto;
  }

  &-item{
    padding: 16px;
    background-color: rgba(#825CE4, .06);
    border-radius: 4px;
    display: flex;

    &.is-duty{
      .workbench-info-item__tag{
        color: #146BF4;
        border-color:#146BF4;
        background-color: #C7DDFF;
      }
      .workbench-info-item__icon{
        background-image: url("../assets/duty-icon.png");
      }
    }

    &.is-exchange{
      .workbench-info-item__tag{
        color: #6c63c1;
        background-color: rgba(#bd99ff, 0.15);
        border-color:#7e37ff;
      }
      .workbench-info-item__icon{
        background-image: url("../assets/exchange-icon.png");
      }
    }

    &:hover .workbench-info-item-operates{
      display: flex;
    }

    &:last-child{
      padding-bottom: 16px;
    }

    &__icon{
      width: 40px;
      height: 40px;
      margin-right: 13px;
      background-size: 100% 100%;
      background-image: url("../assets/memo-icon.png");
    }

    &-header{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    &-content{
      flex: 1;
      position: relative;
    }

    &__title{
      flex: auto;
      font-family: Source Han Sans CN,serif;
      font-weight: bold;
      font-size: 14px;
      color: #202225;
      // width: 200px;
      white-space: nowrap; /* 不换行 */
      overflow: hidden;   /* 隐藏超出内容 */
      text-overflow: ellipsis; /* 使用省略号 */
      margin-right: 16px;
    }

    &__tag{
      display: inline-block;
      padding: 3px 8px;
      font-family: Source Han Sans CN,serif;
      font-weight: 400;
      font-size: 12px;
      color: #07A78B;
      background: rgba(0,194,160,0.14);
      border-radius: 4px;
      border: 1px solid #008B89;
      margin-left: auto;

      &.is-on-durt{
        color: #A19BD7;
        border-color: #C6A9FB;
        background-color: #EEE8F9;
      }

      &.is-administrative-info{
        color: #2173F5;
        border-color:#6FA7FF;
        background-color: #C7DDFF;
      }
    }

    &__content {
      width: 100%;
      margin-bottom: 8px;
      font-family: Source Han Sans CN,serif;
      font-weight: 400;
      font-size: 12px;
      line-height: 1.4;
      letter-spacing: 1px;
      color: #4B5566;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      word-break: break-all;
    }

    &-info{
      display: flex;
      align-items: center;
    }

    &__date{
      font-family: Source Han Sans CN,serif;
      font-weight: 400;
      font-size: 12px;
      color: #4B5566;
    }

    &__line{
      width: 1px;
      height: 10px;
      background: #CCCCCC;
      margin: 0 16px;
    }

    &__user{
      font-family: Source Han Sans CN,serif;
      font-weight: 400;
      font-size: 12px;
      color: rgba(#4B5566,.6);
    }

    &-operates{
      display: none;
      position: absolute;
      top: -16px;
      right: -16px;
      height: calc(100% + 32px);
      flex-direction: column;
      justify-content: center;
      align-items: center;
      row-gap: 6px;
      background-color:rgba(#7756FF,.7);
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }

    &__operate{
      color: #fff;
      padding: 0 3px;
      cursor: pointer;
      font-size: 16px;
    }
  }
  &::before, &::after {
    content: '';
    width: 29px;
    height: 29px;
    background: url("../assets/card-horn-decorate.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    bottom: 0;
  }
  &::after {
    left: initial;
    right: 0;
    transform: rotateZ(-90deg);
  }
}
