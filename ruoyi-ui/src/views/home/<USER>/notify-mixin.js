import { getAllData, setRead } from "@/api/messages";

export default {
  name: "NotifyMixin",
  provide() {
    return {
      getMonitorList: () => {
        return this.monitorList;
      },
      getFlowList: () => {
        return this.flowList || [];
      },
      toMonitors: this.toMonitors,
      toMonitorDetail: this.toMonitorDetail,
      toFlows: this.toFlows,
      toFlowDetail: this.toFlowDetail,
      readAllNotify: this.readAllNotify,
    };
  },
  mounted() {
    const hasNotifyComponents =
      (this.layout || []).filter((item) =>
        ["flow", "monitor"].includes(item.key)
      ).length > 0;
    if (!hasNotifyComponents) {
      return;
    }
    this.getNotifyData();
    this.pollNotifyData();
  },
  beforeDestroy() {
    this.clearNotify();
    this.clearPollNotifyData();
  },
  data() {
    return {
      monitorList: [],
      flowList: [],
      notifyState: {
        timer: null,
        instance: null,
        list: [],
        index: 0,
      },
    };
  },
  methods: {
    toMonitors() {
      this.parentRouter.push({ path: "/operateMonitor/monitorResultInfo" });
    },
    toMonitorDetail() {
      history.pushState({}, "", "/operateMonitor/monitorResultInfo");
    },
    toFlows(type) {
      let path = "/productManage/productFlow";
      switch (type) {
        case "processList":
          path = "/work/own";
          break;
        case "todoProcessList":
          path = "/work/todo";
          break;
        // case "费用支付":
        //   path = "/settlementBusiness/costPay/costPay";
        //   break;
        // case "用印流程":
        //   path = "/settlementBusiness/departmentSeal";
        //   break;
      }
      this.$router.push({ path });
    },
    toFlowDetail(row, activeTab) {
      switch (activeTab) {
        case "processList":
          this.$router.push({
            path: "/workflow/process/detail/" + row.procInsId,
            query: {
              processed: false,
            },
          });
          break;
        case "todoProcessList":
          this.$router.push({
            path: "/workflow/process/detail/" + row.procInsId,
            query: {
              taskId: row.taskId,
              processed: true,
            },
          });
          break;
        // case '外报报送': // 外报
        //   this.parentRouter.push({
        //     name: 'outReportSubmitted',
        //     params: {
        //       from: 'personal',
        //       id: row.info.id
        //     }
        //   })
        //   break
        // case '内报报送': // 内报
        //   this.parentRouter.push({
        //     name: 'inReportSubmitted',
        //     params: {
        //       from: 'personal',
        //       id: row.info.id
        //     }
        //   })
        //   break
        // case '费用支付': // 费用
        //   this.parentRouter.push({ name: 'CostDetail', params: row.info })
        //   break
        // case '产品流程': // 产品
        //   sessionStorage.setItem('flow', JSON.stringify(row.info))
        //   if (row.info.processType === '产品成立') {
        //     this.parentRouter.push({ name: 'Found', params: row.info })
        //   } else if (row.info.processType === '产品分红') {
        //     this.parentRouter.push({ name: 'Dividend', params: row.info })
        //   } else if (row.info.processType === '产品清算') {
        //     this.parentRouter.push({ name: 'Liquidation', params: row.info })
        //   } else if (row.info.processType === '托管行入池') {
        //     this.parentRouter.push({ name: 'TrustPool', params: row.info })
        //   } else if (row.info.processType === '补充协议') {
        //     this.parentRouter.push({ name: 'Supplementary', params: row.info })
        //   }
        //   break
        // case '用印流程': // 部门用印
        //   this.parentRouter.push({
        //     name: 'DepartmentSeal',
        //     params: {
        //       from: 'personal',
        //       id: row.info.id
        //     }
        //   })
        //   break
        default:
          break;
      }
    },
    getNotifyData() {
      if (!this.$store.state?.userInfo?.ncam?.user?.userInfo?.name) {
        return;
      }
      getAllData(this.$store.state.userInfo.ncam.user.userInfo.name).then(
        (res) => {
          if (res.code === 200) {
            this.monitorList = res.result.monitor
              ? res.result.monitor.list
              : [];
            this.flowList = res.result.process.detail || [];
            // notify
            this.notifyState = {
              list: res.result.popUp.listPopUp || [],
              index: 0,
            };
            this.clearNotify();
            this.openNotify();
          }
        }
      );
    },
    clearNotify() {
      this.$notify.closeAll();
    },
    openNotify() {
      const item = this.notifyState.list[this.notifyState.index];
      if (!item) {
        return;
      }
      const self = this;
      self.notifyState.index = this.notifyState.index + 1;
      this.notifyState.instance = this.$notify.info({
        title: item.type,
        message: item.content,
        duration: 0,
        offset: 0,
        onClick: () => {
          setRead({ id: item.id });
          self.clearNotify();
          self.openNotify();
        },
        showClose: false,
      });
    },
    pollNotifyData() {
      this.clearPollNotifyData();
      const self = this;
      this.notifyState.timer = setTimeout(() => {
        self.getNotifyData();
      }, 60000);
    },
    clearPollNotifyData() {
      if (this.notifyState.timer) {
        clearTimeout(this.notifyState.timer);
        this.notifyState.timer = null;
      }
    },
  },
};
