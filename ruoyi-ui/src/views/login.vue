<script>
import LoginDecorate from '../components/Login/LoginDecorate.vue';
import LoginForm from '../components/Login/LoginForm.vue';

export default {
  components: {
    LoginDecorate,
    LoginForm
  }
}

</script>

<template>
  <div class="login-container">
    <LoginDecorate />
    <div class="login-form-block">
      <LoginForm />

    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-container {
  width: 100%;
  height: 100vh;
  padding: 60px 84px;
  display: flex;
  align-items: center;
  position: relative;
  background: url('../assets/images/login-background.png') 100% 100% no-repeat;
  background-size: 100% 100%;

  .login-form-block {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
