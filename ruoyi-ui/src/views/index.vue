<template>
  <section class="workbench-calendar" :class="[`is-${size}`]">
    <header class="workbench-calendar-header">
      <button
        class="workbench__button"
        style="margin-left: auto; margin-right: -12px"
        @click="jumpWork"
      >
        值班管理
      </button>
      <el-button
        class="workbench-calendar__event"
        type="text"
        size="small"
        icon="el-icon-plus"
        @click="handleCreateEventOpen"
      >
        添加日程
      </el-button>
    </header>
  </section>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      // 版本号
    };
  },
  methods: {
    // goTarget(href) {
    //   window.open(href, "_blank");
    // },
  },
};
</script>

<style lang="scss">
.workbench-calendar {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px;
  // background: url("../assets/card-decorate.png") top right no-repeat;
  background-size: 214px 76px;
  background-color: #fff;
  box-shadow: 1px 2px 13px 1px rgba(154, 172, 194, 0.11);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  position: relative;

  &-header,
  &-info,
  &-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  &-info {
    margin: 14px 0;
  }

  &-tab,
  &-tags {
    display: flex;
    align-items: center;
  }

  &-tab {
    border-radius: 4px;
    padding: 2px;
    background-color: #fff;
    margin-right: 16px;

    &__item {
      padding: 6px 8px;
      cursor: pointer;
      color: #333a43;
      font-size: 14px;
      font-weight: 400;

      &.is-active {
        color: #fff;
        border-radius: 4px;
        background-color: rgba(#825ce4, 0.7);
      }
    }
  }

  &-tags {
    column-gap: 6px;

    &__item {
      padding: 10px 6px;
      background-repeat: no-repeat;
      background-size: cover;
      font-family: Source Han Sans CN, serif;
      font-size: 14px;
      font-weight: 400;

      &.is-a {
        color: #2173f5;
        // background-image: url("../assets/a-share-bg.png");
      }
      &.is-etf {
        color: #0099a7;
        // background-image: url("../assets/etf-bg.png");
      }
      &.is-amer {
        color: #e3241b;
        // background-image: url("../assets/amer-share-bg.png");
      }
    }
  }

  &__line {
    width: 1px;
    height: 32px;
    background: #eeeeee;
    margin-left: auto;
    margin-right: 20px;
  }

  &__date {
    font-family: Source Han Sans CN Bold, serif;
    font-weight: bold;
    font-size: 20px;
    color: #202225;
  }

  &__day {
    margin: 0 auto 0 86px;
    font-family: Source Han Sans CN, serif;
    font-weight: bold;
    font-size: 14px;
    color: #333333;
    cursor: pointer;
  }

  &__next,
  &__prev {
    cursor: pointer;
    color: #787878;
    font-weight: bold;

    &:hover {
      color: #999999;
    }
  }

  &__prev {
    margin-right: 22px;
  }

  &__event {
    width: 92px;
    height: 32px;
    font-family: Source Han Sans CN, serif;
    font-weight: bold;
    font-size: 14px;
    color: #333333;

    &:focus,
    &:hover {
      color: #aba7ff;
    }

    .el-icon-plus {
      color: #797979;
    }

    & > span {
      margin-left: 4px;
    }
  }

  &-main {
    flex: auto;
    overflow: hidden;
    width: 100%;
    background: rgba(#825ce4, 0.06);
    border-radius: 8px;
    margin-bottom: 10px;
    box-sizing: border-box;
    padding: 16px;
    display: flex;
    flex-direction: column;
  }

  &__row {
    flex: 1;
    width: 100%;
    display: flex;
    min-height: 46px;
    align-items: flex-start;
    // align-items: center;
    justify-content: space-between;
  }

  &-row-week {
    flex: auto 0 0;
    align-items: center;
  }

  &__col {
    flex: 1;
    font-weight: 500;
    font-size: 19px;
    color: rgba(#000000, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    // display: inline-block;
    // text-align: center;
    cursor: pointer;
    position: relative;
    font-family: "DIN", serif;

    & > span {
      width: 42px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid transparent;
    }

    &.is-week {
      font-weight: bold;
      font-size: 14px;
      font-family: Source Han Sans CN, serif;
      color: #4b5566;
    }

    &.is-reset {
      color: #ffb535;
    }

    &.is-prev,
    &.is-next {
      color: rgba(#4b5566, 0.3);
    }

    &.is-today > span {
      color: #3d2282;
      width: 100%;
      width: 42px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
      // text-align: center;
      // display: inline-block;
      // line-height: 39px;
      border-radius: 5px;
      border: 2px solid #825ce4;
      background: rgba(#825ce4, 0.2);
    }
  }

  &__col.is-today &__close {
    top: -5px;
    right: -3px;
  }

  &__col.is-today &-dots {
    bottom: 5px;
  }

  &__close {
    width: 23px;
    height: 23px;
    display: block;
    border-radius: 50%;
    top: -16px;
    right: 0;
    text-align: center;
    line-height: 23px;
    font-family: Source Han Sans CN, serif;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    position: absolute;
    z-index: 5;

    &.is-a {
      background-color: #66b4ff;
    }
    &.is-eft {
      background-color: #4ad7c8;
    }
    &.is-amer {
      background-color: #ff8d8d;
    }
    &.is-a-with-eft {
      background: linear-gradient(
        to bottom,
        #66b4ff 50%,
        /* 左边蓝色 */ #4ad7c8 50% /* 右边绿色 */
      );
    }
    &.is-a-with-amer {
      background: linear-gradient(
        to bottom,
        #66b4ff 50%,
        /* 左边蓝色 */ #ff8d8d 50% /* 右边红色 */
      );
    }
    &.is-eft-with-amer {
      background: linear-gradient(
        to bottom,
        #4ad7c8 50%,
        /* 左边绿色 */ #ff8d8d 50% /* 右边红色 */
      );
    }
    &.is-all {
      background: linear-gradient(
        to bottom,
        #66b4ff 0%,
        /* 顶部蓝色 */ #66b4ff 33%,
        /* 蓝色 33% */ #4ad7c8 33%,
        /* 中部绿色 */ #4ad7c8 66%,
        /* 绿色 66% */ #ff8d8d 66%,
        /* 底部红色 */ #ff8d8d 100% /* 红色 100% */
      );
    }
  }

  &-dots {
    position: absolute;
    left: 50%;
    bottom: -5px;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    column-gap: 2px;

    & > i {
      width: 4px;
      height: 4px;
      border-radius: 50%;

      &.is-on-durt {
        background-color: #00cd52;
      }

      &.is-new-share {
        background-color: #ff60ba;
      }

      &.is-event {
        background-color: #ff8820;
      }
    }
  }

  &-events {
    height: calc(100% - 52px);
    margin: 4px 4px 0;
    display: flex;
    align-items: center;
    flex-direction: column;
    row-gap: 4px;
    overflow-y: auto;
    overflow-x: hidden;
    color: #9a9a9a;
    font-size: 12px;
  }

  &-footer {
    &__item {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 15px;
    }

    &__dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 6px;

      &.is-on-durt {
        background-color: #00cd52;
      }

      &.is-new-share {
        background-color: #ff60ba;
      }

      &.is-event {
        background-color: #ff8820;
      }
    }

    &__label {
      font-family: Source Han Sans CN, serif;
      font-weight: 400;
      font-size: 14px;
      color: #4b5566;
    }
  }
  &::before,
  &::after {
    content: "";
    width: 29px;
    height: 29px;
    // background: url("../assets/card-horn-decorate.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    bottom: 0;
  }
  &::after {
    left: initial;
    right: 0;
    transform: rotateZ(-90deg);
  }
}
</style>
