<template>
  <div class="scheduling">
    <div class="scheduling-organization">
      <div class="scheduling-organization-header">组织机构</div>
      <div class="scheduling-organization-tree">
        <el-tree
          :data="treeData"
          node-key="id"
          :props="defaultProps"
          highlight-current
          :default-expanded-keys="expandedKeys"
          :default-checked-keys="selectedKey"
          @node-click="handleNodeClick"
          :show-checkbox="false"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <i
              class="el-icon-office-building scheduling-organization-tree-icon"
            />
            <span>{{ data.orgName }}</span>
          </span>
        </el-tree>
      </div>
    </div>

    <div class="scheduling-user">
      <div class="scheduling-user-header">姓名</div>
      <div class="scheduling-user-main">
        <el-radio-group
          v-model="activeUser"
          size="medium"
          style="padding: 10px"
        >
          <template v-if="userList.length > 0">
            <div
              class="scheduling-user-item"
              v-for="item in userList"
              :key="item.id"
              :class="{ active: activeUser === item.id }"
            >
              <el-radio :label="item.id" style="width: 100%">
                <div style="display: flex; align-items: center">
                  <div class="scheduling-user-item-logo">
                    {{ item.orgName.slice(0, 1) }}
                  </div>
                  <div
                    class="scheduling-user-item-content-name"
                    :title="item.orgName"
                  >
                    {{ item.orgName }}
                  </div>
                </div>
              </el-radio>
            </div>
          </template>
          <template v-else>
            <div class="empty">暂无数据</div>
          </template>
        </el-radio-group>
      </div>
    </div>

    <!-- 这里用你的 Calendar 组件，props 不变 -->
    <Calendar
      :organizationId="organizationId"
      :userId="activeUser"
      :userList="userList"
    />
  </div>
</template>

<script>
import Calendar from "./Calendar.vue";
import { getTreeOrgResponse, getOrgAllocated } from "@/api/organization";

export default {
  name: "Scheduling",
  components: {
    Calendar,
  },
  data() {
    return {
      treeData: [],
      selectedKey: [],
      expandedKeys: [], // 默认展开节点ID数组，可根据需要设置
      userList: [],
      activeUser: "",
      defaultProps: {
        children: "children",
        label: "orgName",
      },
    };
  },
  computed: {
    organizationId() {
      return this.selectedKey.length > 0 ? this.selectedKey[0] : "";
    },
  },
  methods: {
    async loadTreeData() {
      try {
        this.treeData = (await getTreeOrgResponse()).data;
        // 如果需要默认展开全部或部分节点，可递归取id设置expandedKeys
        this.expandedKeys = this.getAllIds(this.treeData);
        // this.selectedKey = [this.getSelectOneKey(this.treeData).id];

        console.log(
          "🚀 ~ loadTreeData ~ this.getSelectOneKey(this.treeData):",
          this.getSelectOneKey(this.treeData).id
        );
        const data = this.getSelectOneKey(this.treeData);

        console.log('🚀 ~ loadTreeData ~ data:', data)

        this.userList = data.userlist;
        this.selectedKey = [data.id];

        // console.log("🚀 ~ loadTreeData ~ this.selectedKey:", this.selectedKey);
      } catch (e) {
        console.error(e);
      }
    },
    getSelectOneKey(tree) {
      if (!tree.length) return null;
      if (!tree[0].children.length) return tree[0];
      for (const item of tree) {
        if (!item.children || item.children.length === 0) {
          return item;
        }

        const found = this.getSelectOneKey(item.children);
        if (found) return found;
      }

      return null;
    },
    getAllIds(tree) {
      let ids = [];
      tree.forEach((item) => {
        ids.push(item.id);
        if (item.children && item.children.length > 0) {
          ids = ids.concat(this.getAllIds(item.children));
        }
      });
      return ids;
    },
    async handleNodeClick(data, node) {
      if (data.orgType === "3") {
        this.selectedKey = [data.id];
        try {
          this.userList = data.userlist;
          this.activeUser = data.userlist?.[0]?.id;
        } catch (e) {
          console.error(e);
        }
      }
    },
  },
  mounted() {
    this.loadTreeData();
  },
};
</script>

<style scoped lang="scss">
.scheduling {
  height: 100%;
  display: flex;

  .scheduling-organization {
    background: #fafafa;
    width: 300px;
    display: flex;
    flex-direction: column;
    padding: 0;

    .scheduling-organization-header {
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      font-size: 16px;
      background: #f7f8fc;
      border-bottom: 1px solid #e4e7ed;
    }

    .scheduling-organization-tree {
      flex: 1;
      padding: 20px;
      overflow-y: auto;

      .scheduling-organization-tree-icon {
        margin-right: 5px;
        color: #888c92;
      }

      .custom-tree-node {
        display: flex;
        align-items: center;
      }

      .el-tree-node.is-current .custom-tree-node {
        background: #e6f7ff !important;
        border-right: 2px solid #825ce4;
      }
    }
  }

  .scheduling-user {
    width: 200px;
    box-sizing: border-box;
    border-right: 1px solid #dcdfe6;
    display: flex;
    flex-direction: column;

    .scheduling-user-header {
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      font-size: 16px;
      background: #f7f8fc;
      border-bottom: 1px solid #e4e7ed;
    }

    .scheduling-user-main {
      flex: 1;
      box-sizing: border-box;
      padding: 10px;
    }

    .scheduling-user-item {
      height: 60px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 0 20px;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background: #f0f6ff;
      }

      &.active {
        background: rgba(21, 94, 255, 0.1);
      }

      .el-radio {
        width: 100%;
        display: flex;
        align-items: center;

        .el-radio__label {
          width: calc(100% - 24px);
          display: flex;
          align-items: center;
        }
      }

      .scheduling-user-item-logo {
        height: 32px;
        width: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        border-radius: 50%;
        background: #825ce4;
        color: #fff;
        font-weight: bold;
      }

      .scheduling-user-item-content-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .empty {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #909399;
      font-size: 14px;
    }
  }
}
</style>
