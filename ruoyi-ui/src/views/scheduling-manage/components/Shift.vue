<template>
  <div class="shift">
    <!-- 查询表单 -->
    <div class="shift-action">
      <div class="shift-action-form">
        <span>名称:</span>
        <el-input v-model="query.name" clearable placeholder="名称"></el-input>
      </div>
      <div class="shift-action-btn">
        <el-button @click="handleReset" icon="el-icon-refresh-right" size="small">重置</el-button>
        <el-button type="primary" @click="loadData" icon="el-icon-search" size="small">查询</el-button>
      </div>
    </div>

    <!-- 新增按钮 -->
    <div style="margin: 5px 0">
      <el-button type="primary" size="small" icon="el-icon-plus" @click="openDialog()">新增</el-button>
      <el-button type="primary" size="small" @click="openInitModal()">初始化日历</el-button>
      <el-button type="primary" size="small" @click="openHolidayModal()">更新节假日</el-button>
    </div>

    <!-- 表格 -->
    <el-table :data="shiftList" style="width: 100%" :loading="loading" border>
      <el-table-column prop="name" label="班次名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="orgName" label="组织" show-overflow-tooltip></el-table-column>
      <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="openDialog(scope.row.id)">编辑</el-button>
          <el-button type="text" size="small" style="color: red" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination style="margin-top: 10px; text-align: right" background layout="prev, pager, next"
      :total="pagination.total" :page-size="pagination.pageSize" :current-page.sync="pagination.current"
      @current-change="pageChange"></el-pagination>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="handleDialogClose">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="60px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" maxlength="30" show-word-limit placeholder="名称"></el-input>
        </el-form-item>

        <el-form-item label="组织" prop="orgId">
          <ElTreeSelect v-model="formData.orgId" :options="treeData"
            :props="{ label: 'label', children: 'children', checkStrictly: false, value: 'value', emitPath: false }"
            clearable placeholder="请选择组织" />

        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" placeholder="排序"></el-input-number>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="formData.remark" placeholder="备注" rows="3"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="handleDialogOk">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="初始化日历" :visible.sync="initVisible" :before-close="handleInitClose">
      <div class="rule-manage-init">
        <el-select v-model="initYearStart" placeholder="开始年份" @change="initYearStartChange">
          <el-option v-for="item in initYearList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <div class="rule-manage-init-text">至</div>
        <el-select v-model="initYearEnd" placeholder="结束年份">
          <el-option v-for="item in initYearList" :key="item" :label="item" :value="item"
            :disabled="initYearStart && item < initYearStart"></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="initVisible = false">取消</el-button>
        <el-button type="primary" :loading="initLoading" @click="initCalendarConfirm">确定</el-button>
      </span>
    </el-dialog>

    <!-- 更新节假日对话框 -->
    <el-dialog title="更新节假日" :visible.sync="holidayVisible" :before-close="handleHolidayClose">
      <div class="rule-manage-holiday">
        <div class="rule-manage-holiday-year"
          style="display: flex; align-items: center; gap: 10px;margin-bottom: 10px;">
          <span>年份</span>
          <el-select v-model="currentYear" placeholder="请选择年份" style="width: 200px">
            <el-option v-for="item in initYearList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
          <el-tooltip content="请选择对应年份节日日期">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>

        <el-form ref="holidayFormRef" :model="holidayFormData" :rules="holidayRules" label-width="150px">
          <el-form-item v-for="key in Object.keys(holidayFormData)" :key="key" :prop="key" :label="labelMap[key]">
            <el-date-picker v-model="holidayFormData[key]" type="date" style="width: 100%" :placeholder="labelMap[key]"
              value-format="yyyy-MM-dd" />
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="holidayVisible = false">取消</el-button>
        <el-button type="primary" :loading="holidayLoading" @click="updateHolidayConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getShiftList, saveShift, removeShift, updateShift, getShiftDetail } from '@/api/scheduling-manage'
import { getTreeOrgResponse } from '@/api/organization'
import { initCalendar, getCalendarExistYear, editCalendar } from '@/api/rule-manage'
import ElTreeSelect from '../../../components/ElTreeSelect/index.vue'


export default {
  name: 'ShiftManage',
  components: {
    ElTreeSelect
  },
  data() {
    return {
      labelMap: {
        name: "名称",
        indicatorId: "脚本",
        orderSort: "排序",
        newYearDayStart: "元旦开始日期",
        newYearDayEnd: "元旦结束日期",
        springFestivalStart: "春节开始日期",
        springFestivalEnd: "春节结束日期",
        tombSweepingDayStart: "清明节开始日期",
        tombSweepingDayEnd: "清明节结束日期",
        labourDayStart: "劳动节开始日期",
        labourDayEnd: "劳动节结束日期",
        dragonBoatFestivalStart: "端午节开始日期",
        dragonBoatFestivalEnd: "端午节结束日期",
        midAutumnFestivalStart: "中秋节开始日期",
        midAutumnFestivalEnd: "中秋节结束日期",
        nationalDayStart: "国庆节开始日期",
        nationalDayEnd: "国庆节结束日期",
        typeVal: "类型",
      },
      query: {
        page: 1,
        pageSize: 20,
        name: "",
      },
      shiftList: [],
      loading: false,
      pagination: {
        total: 0,
        pageSize: 20,
        current: 1,
      },
      dialogVisible: false,
      dialogLoading: false,
      dialogTitle: "新增班次",
      formData: {
        id: null,
        name: "",
        orgId: '', // 这里是数组，el-cascader 绑定值
        remark: "",
        sort: 0,
      },
      formRules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        orgId: [
          {
            type: "string",
            required: true,
            message: "请选择组织",
            trigger: "change",
          },
        ],
        sort: [{ required: true, message: "请输入排序", trigger: "change" }],
      },
      treeData: [],
      initVisible: false,
      initLoading: false,
      initYearStart: null,
      initYearEnd: null,
      initYearList: [],

      // 更新节假日
      holidayVisible: false,
      holidayLoading: false,
      currentYear: "",
      existYearList: [],
      holidayFormData: {
        newYearDayStart: "",
        newYearDayEnd: "",
        springFestivalStart: "",
        springFestivalEnd: "",
        tombSweepingDayStart: "",
        tombSweepingDayEnd: "",
        labourDayStart: "",
        labourDayEnd: "",
        dragonBoatFestivalStart: "",
        dragonBoatFestivalEnd: "",
        midAutumnFestivalStart: "",
        midAutumnFestivalEnd: "",
        nationalDayStart: "",
        nationalDayEnd: "",
      },
      holidayRules: {
        newYearDayStart: [
          { required: true, message: "请选择元旦开始日期", trigger: "blur" },
          { validator: this.validator },
        ],
        newYearDayEnd: [
          { required: true, message: "请选择元旦结束日期", trigger: "blur" },
          { validator: this.validator },
        ],
        springFestivalStart: [
          { required: true, message: "请选择春节开始日期", trigger: "blur" },
          { validator: this.validator },
        ],
        springFestivalEnd: [
          { required: true, message: "请选择春节结束日期", trigger: "blur" },
          { validator: this.validator },
        ],
        tombSweepingDayStart: [
          { required: true, message: "请选择清明节开始日期", trigger: "blur" },
          { validator: this.validator },
        ],
        tombSweepingDayEnd: [
          { required: true, message: "请选择清明节结束日期", trigger: "blur" },
          { validator: this.validator },
        ],
        labourDayStart: [
          { required: true, message: "请选择劳动节开始日期", trigger: "blur" },
          { validator: this.validator },
        ],
        labourDayEnd: [
          { required: true, message: "请选择劳动节结束日期", trigger: "blur" },
          { validator: this.validator },
        ],
        dragonBoatFestivalStart: [
          { required: true, message: "请选择端午节开始日期", trigger: "blur" },
          { validator: this.validator },
        ],
        dragonBoatFestivalEnd: [
          { required: true, message: "请选择端午节结束日期", trigger: "blur" },
          { validator: this.validator },
        ],
        midAutumnFestivalStart: [
          { required: true, message: "请选择中秋节开始日期", trigger: "blur" },
          { validator: this.validator },
        ],
        midAutumnFestivalEnd: [
          { required: true, message: "请选择中秋节结束日期", trigger: "blur" },
          { validator: this.validator },
        ],
        nationalDayStart: [
          { required: true, message: "请选择国庆节开始日期", trigger: "blur" },
          { validator: this.validator },
        ],
        nationalDayEnd: [
          { required: true, message: "请选择国庆节结束日期", trigger: "blur" },
          { validator: this.validator },
        ],
      },
    };
  },
  methods: {
    openInitModal() {
      this.initYearStart = undefined;
      this.initYearEnd = undefined;
      this.initVisible = true;
    },
    handleInitClose() {
      this.initVisible = false;
    },
    handleHolidayClose() {
      this.holidayVisible = false;
    },
    getInitYear() {
      const currentYear = new Date().getFullYear();
      this.initYearList = Array.from(
        { length: 101 },
        (_, i) => currentYear + i
      );
    },

    // 弹窗：年份选择变更
    initYearStartChange(val) {
      if (this.initYearEnd && this.initYearEnd < val) {
        this.initYearEnd = val;
      }
    },

    // 弹窗：初始化日历提交
    async initCalendarConfirm() {
      if (!this.initYearStart) {
        this.$message.warning("请选择起始年份");
        return;
      }
      if (!this.initYearEnd) {
        this.$message.warning("请选择结束年份");
        return;
      }
      this.initLoading = true;
      try {
        await initCalendar({
          startYear: String(this.initYearStart),
          endYear: String(this.initYearEnd),
        });
        this.$message.success("初始化成功");
        this.initVisible = false;
      } catch (e) {
        console.error(e);
      } finally {
        this.initLoading = false;
      }
    },

    // 弹窗：打开节假日更新弹窗
    async openHolidayModal() {
      try {
        this.existYearList = (await getCalendarExistYear()).data;
        if (this.existYearList.length === 0) {
          this.$message.error("当前无已初始化年份");
          return;
        }
        this.currentYear = this.existYearList[0];
        this.holidayFormData = Object.assign({}, this.holidayFormData); // 重置表单
        this.holidayVisible = true;
      } catch (e) {
        console.error(e);
      }
    },

    // 弹窗：节假日表单校验器
    validator(rule, value, callback) {
      const regex = /^\d{4}-\d{2}-\d{2}$/;
      if (!regex.test(value)) {
        return callback("请输入正确的时间格式");
      }
      const [year, month, day] = value.split("-").map(Number);
      const date = new Date(year, month - 1, day);
      if (
        date.getFullYear() !== year ||
        date.getMonth() + 1 !== month ||
        date.getDate() !== day
      ) {
        return callback("请输入正确的时间格式");
      }
      if (String(year) != this.currentYear) {
        return callback("请选择当前选择年份");
      }
      callback();
    },

    // 弹窗：提交节假日更新
    async updateHolidayConfirm() {
      this.$refs.holidayFormRef.validate(async (valid) => {
        if (!valid) return;
        this.holidayLoading = true;
        try {
          await editCalendar(this.holidayFormData);
          this.$message.success("节假日更新成功");
          this.holidayVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.holidayLoading = false;
        }
      });
    },
    async loadData() {
      this.loading = true;
      try {
        const res = (await getShiftList(this.query)).data;
        this.shiftList = res.records || [];
        this.pagination.total = res.total || 0;
        this.pagination.current = res.current || this.query.page;
        this.pagination.pageSize = res.size || this.query.pageSize;
      } catch (e) {
        console.error(e);
      }
      this.loading = false;
    },
    handleReset() {
      this.query = {
        page: 1,
        pageSize: 20,
        name: "",
      };
      this.loadData();
    },
    pageChange(page) {
      this.query.page = page;
      this.loadData();
    },
    async handleDelete(id) {
      this.$confirm("确定删除该班次？", "提示", {
        type: "warning",
      })
        .then(async () => {
          try {
            await removeShift(id);
            this.$message.success("删除成功");
            this.loadData();
          } catch (e) {
            console.error(e);
          }
        })
        .catch(() => {
          // 取消删除
        });
    },
    async openDialog(id) {
      if (id) {
        try {
          const data = (await getShiftDetail(id)).data;
          this.formData = {
            id: data.id,
            name: data.name || "",
            orgId: data.orgId || '',
            remark: data.remark || "",
            sort: Number(data.sort) || 0,
          };
          this.dialogTitle = data.name || "编辑班次";
        } catch (e) {
          console.error(e);
        }
      } else {
        this.formData = {
          id: null,
          name: "",
          orgId: "",
          remark: "",
          sort: 0,
        };
        this.dialogTitle = "新增班次";
      }
      this.dialogVisible = true;
    },
    handleDialogClose() {
      this.dialogVisible = false;
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
    },
    handleDialogOk() {
      this.$refs.formRef.validate(async (valid) => {
        if (!valid) return;
        this.dialogLoading = true;
        try {
          // 提交时把 orgId 数组转成单个字符串（只取第一个）
          const submitData = {
            ...this.formData,
            orgId: this.formData.orgId || "",
          };
          if (submitData.id) {
            await updateShift(submitData);
            this.$message.success("修改成功");
          } else {
            await saveShift(submitData);
            this.$message.success("新增成功");
          }
          this.loadData();
          this.dialogVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.dialogLoading = false;
        }
      });
    },
    // 递归把接口返回的 org 树转成 el-cascader 需要的格式
    convertTreeData(data) {
      return data.map((item) => {
        return {
          value: item.id,
          label: item.orgName,
          children: item.children?.length
            ? this.convertTreeData(item.children)
            : undefined,
        };
      });
    },
    async loadOrgTree() {
      try {
        const tree = (await getTreeOrgResponse()).data;
        this.treeData = this.convertTreeData(tree);
        console.log(this.treeData)
      } catch (e) {
        console.error(e);
      }
    },
  },
  mounted() {
    this.loadData();
    this.loadOrgTree();
    this.getInitYear();
  },
};
</script>

<style scoped lang="scss">
.shift {
  height: 700px;
  display: flex;
  flex-direction: column;
  row-gap: 5px;

  .shift-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 8px;

    .shift-action-form {
      display: flex;
      align-items: center;

      span {
        white-space: nowrap;
        margin-right: 8px;
      }
    }

    .shift-action-btn {
      display: flex;
    }
  }
}

.rule-manage-init {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rule-manage-init-text {
  padding: 0 10px;
}

.rule-manage-holiday {
  margin-top: 10px;
}
</style>
