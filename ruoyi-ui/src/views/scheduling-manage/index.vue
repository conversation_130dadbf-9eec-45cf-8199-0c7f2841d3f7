<template>
  <div class="scheduling-manage">
    <el-tabs v-model="activeKey" @tab-click="handleTabClick">
      <el-tab-pane label="排班管理" name="1"></el-tab-pane>
      <el-tab-pane label="班次管理" name="2"></el-tab-pane>
    </el-tabs>

    <div class="scheduling-manage-content">
      <Scheduling v-if="activeKey === '1'"></Scheduling>
      <Shift v-else></Shift>
    </div>
  </div>
</template>

<script>
import Scheduling from './components/Scheduling.vue'
import Shift from './components/Shift.vue'

export default {
  name: 'SchedulingManage',
  components: {
    Scheduling,
    Shift
  },
  data() {
    return {
      activeKey: '1'
    }
  },
  methods: {
    handleTabClick(tab) {
      this.activeKey = tab.name
    }
  }
}
</script>

<style scoped lang="scss">
.scheduling-manage {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  background-color: #fff;

  .scheduling-manage-content {
    flex: 1;
  }
}
</style>
