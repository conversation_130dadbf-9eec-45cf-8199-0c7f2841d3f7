<template>
  <div class="app-container">

    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="processName">
        <el-input v-model="queryParams.processName" placeholder="请输入流程名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="接收时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          v-hasPermi="['workflow:process:claimExport']" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="claimList">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="任务编号" align="center" prop="taskId" :show-overflow-tooltip="true"/> -->
      <el-table-column label="流程标题" align="center" prop="processName" :show-overflow-tooltip="true" />
      <el-table-column label="流程名称" align="center" prop="procDefName" />
      <el-table-column label="任务节点" align="center" prop="taskName" />
      <el-table-column label="流程版本" align="center">
        <template slot-scope="scope">
          <el-tag size="medium">v{{ scope.row.procDefVersion }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="流程发起人" align="center">
        <template slot-scope="scope">
          <label>{{ scope.row.startUserName }}</label>
        </template>
      </el-table-column>
      <el-table-column label="接收时间" align="center" prop="createTime" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-s-claim" @click="handleClaim(scope.row)"
            v-hasPermi="['workflow:process:claim']">签收
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { listClaimProcess } from '@/api/workflow/process';
import { claimTask } from '@/api/workflow/task';

export default {
  name: 'Claim',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 流程待办任务表格数据
      claimList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        processName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.getList()
    })
  },
  methods: {
    /** 查询流程定义列表 */
    getList() {
      this.loading = true;
      listClaimProcess(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.claimList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 签收 */
    handleClaim(row) {
      claimTask({ taskId: row.taskId }).then(response => {
        this.$modal.msgSuccess(response.msg);
        this.$router.push({
          path: '/work/todo'
        })
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('workflow/process/claimExport', {
        ...this.queryParams
      }, `wf_claim_process_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped></style>
