<template>
  <el-dialog :visible.sync="visible" :title="`${formData.id ? '编辑' : '生成'}合同`">
    <el-form :model="formData" :rules="rules" label-width="100px">
      <el-form-item prop="contractName" label="合同名称">
        <el-input size='small' v-model="formData.contractName"></el-input>
      </el-form-item>
      <el-form-item prop="templateId" label="合同模板">
        <el-select size="small" v-model="formData.templateId" style="width: 100%;" :disabled="!!formData.id"
          @change="formData.parameterSetId = ''">
          <el-option v-for="item in templateList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="parameterSetId" label="参数集">
        <el-select size="small" v-model="formData.parameterSetId" style="width: 100%;" :disabled="!!formData.id">
          <el-option v-for="item in parameterList" :key="item.value" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="productCode" label="产品代码">
        <el-input size='small' v-model="formData.productCode"></el-input>
      </el-form-item>
      <el-form-item prop="productName" label="产品名称">
        <el-input size='small' v-model="formData.productName"></el-input>
      </el-form-item>
      <el-form-item prop="productType" label="产品类型">
        <el-select size="small" v-model="formData.productType" clearable placeholder="请选择" style="width: 100%;">
          <el-option label="受托产品" value="受托产品"></el-option>
          <el-option label="组合类产品" value="组合类产品"></el-option>
          <el-option label="货币类产品" value="货币类产品"></el-option>
          <el-option label="债权计划" value="债权计划"></el-option>
          <el-option label="资产支持计划" value="资产支持计划"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="investmentManager" label="投资经理">
        <el-select size="small" v-model="formData.investmentManager" style="width: 100%;">
          <el-option v-for="item in userList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="productManager" label="产品经理">
        <el-select size="small" v-model="formData.productManager" style="width: 100%;">
          <el-option v-for="item in userList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm" :loading="btnLoading">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { Message } from 'element-ui'
import { getTemplateList, getParamsList, initiateContract } from '../../api/contractManagement'
import { listUser } from '../../api/system/user'

export default {
  name: 'ContractModal',
  data() {
    return {
      userList: [],
      list: [],
      templateList: [],
      parameterList: [],
      visible: false,
      formData: {
        contractName: '',
        templateId: "",
        productCode: "",
        productName: "",
        productType: "",
        investmentManager: "",
        productManager: "",
        parameterSetId: ""
      },
      btnLoading: false,
      rules: {
        contractName: [{ required: true, message: '请输入合同名称' }],
        templateId: [{ required: true, message: '请选择合同模板' }],
        parameterSetId: [{ required: true, message: '请选择参数集' }],
        productCode: [{ required: true, message: '请输入产品代码' }],
        productName: [{ required: true, message: '请输入产品名称' }],
        productType: [{ required: true, message: '请输入产品类型' }],
      }
    }
  },
  watch: {
    'formData.templateId'(val, oVal) {
      this.parameterList = []
      if (val) {
        this.loadParamsList(val)
      }
    }
  },
  mounted() {
    this.loadTemplateList()
    this.loadUserList()
  },
  methods: {
    loadUserList() {
      listUser({ pageNum: 1, pageSize: 99999 }).then(res => {
        this.userList = res.rows.map(row => {
          return {
            label: row.nickName,
            value: row.userId
          }
        })
      })
    },
    openModal(row) {
      this.formData = row
      this.visible = true
    },
    confirm() {
      this.btnLoading = true
      initiateContract(this.formData).then(() => {
        Message.success('生成成功')
        this.loadData()
      }).finally(() => {
        this.btnLoading = false

      })
    },
    loadTemplateList() {
      getTemplateList({ current: 1, size: 99999 }).then(res => {
        this.templateList = res.data.records.map(row => {
          return {
            label: row.templateName,
            value: row.id
          }
        })
      })
    },
    loadParamsList(id) {
      getParamsList(id).then(res => {
        this.parameterList = res.data.map(row => {
          return {
            label: row.setCode,
            value: row.id
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
