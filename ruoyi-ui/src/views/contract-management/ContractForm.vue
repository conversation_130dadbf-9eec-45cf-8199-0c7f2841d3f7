<template>
  <div class="contract-form" v-loading="loading">
    <div class="form-back" @click="goBack">
      <i class="el-icon-back"></i>
      返回
    </div>
    <div class="form-block" id="form-block-basic-info">
      <div class="form-block-title">基础信息</div>
      <div class="form-block-main">
        <el-form :model="formData" label-position="left" label-width="100px">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="产品名称">
                <el-input v-model="formData.productName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <el-button @click="toView">合同预览</el-button>
                <el-button @click="toDownload">合同下载</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="form-block" id="form-block-basic-clause">
      <div class="form-block-title">基础条款</div>
      <div class="form-block-main">
        <el-form :model="formData" label-position="left" label-width="100px">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="产品全称">
                <el-input v-model="formData.productFullName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品简称">
                <el-input v-model="formData.productShortName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="英文名称">
                <el-input v-model="formData.productEnglishName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="英文简称">
                <el-input v-model="formData.productEnglishShortName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="产品代码">
                <el-input v-model="formData.productCode"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组合代码">
                <el-input v-model="formData.portfolioCode"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="资产代码">
                <el-input v-model="formData.assetCode"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="form-block" id="form-block-account-info">
      <div class="form-block-title">账户信息</div>
      <div class="form-block-main">
        <el-form :model="formData" label-position="left" label-width="100px">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="托管机构简称">
                <el-input v-model="formData.custodianShortName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="托管机构名称">
                <el-input v-model="formData.custodianName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="托管机构代码">
                <el-input v-model="formData.custodianCode"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="托管账户名称">
                <el-input v-model="formData.custodianAccountName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="托管账户户号">
                <el-input v-model="formData.custodianAccountNumber"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行">
                <el-input v-model="formData.custodianBankName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="大额支付号">
                <el-input v-model="formData.largePaymentNumber"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="form-block" id="form-block-contract-clause">
      <div class="form-block-title">合同条款</div>
      <div class="form-block-main">
        <el-form :model="formData" label-position="left" label-width="100px">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="合同编号">
                <el-input v-model="formData.contractNumber"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同名称">
                <el-input v-model="formData.contractName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="合同分类">
                <el-input v-model="formData.contractCategory"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同状态">
                <el-input v-model="formData.contractStatus"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="合同签署日期">
                <el-input v-model="formData.signingDate"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同生效日期">
                <el-input v-model="formData.effectiveDate"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="合同到期日期">
                <el-input v-model="formData.expiryDate"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同续签日期">
                <el-input v-model="formData.renewalDate"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="合同用印形式">
                <el-input v-model="formData.sealMethod"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用印状态">
                <el-input v-model="formData.sealStatus"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="quick-navigate">
      <div class="quick-navigate-item" v-for="item in anchorList" :key="item.name" @click="toHash(item.hash)">{{
        item.name }}</div>
      <div class="quick-navigate-item" @click="toTop"><i class="el-icon-arrow-up"></i>返回顶部</div>
    </div>
    <div class="footer">
      <el-button type="primary" @click="confirm">保存</el-button>
    </div>
    <WordModal ref="WordModalRef" />
  </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import { getLocalDownloadUrl } from '../../api/onlyoffice';
import WordModal from './components/WordModal.vue';
import { getMasterContractDetail, updateMasterContract } from '../../api/contractManagement';
import { Message } from 'element-ui';

export default {
  name: 'ContractForm',
  components: {
    WordModal
  },
  data() {
    return {
      formData: { productName: '', contractName: '', productFullName: '', productShortName: '', productEnglishName: '', productEnglishShortName: '', productCode: '', portfolioCode: '', assetCode: '', custodianShortName: '', custodianName: '', custodianCode: '', custodianAccountName: '', custodianAccountNumber: '', custodianBankName: '', largePaymentNumber: '', contractNumber: '', contractCategory: '', contractStatus: '', signingDate: '', effectiveDate: '', expiryDate: '', renewalDate: '', sealMethod: '', sealStatus: '' },
      anchorList: [{ name: '基础信息', hash: 'form-block-basic-info' }, { name: '基础条款', hash: 'form-block-basic-clause' }, { name: '账户信息', hash: 'form-block-account-info' }, { name: '合同条款', hash: 'form-block-contract-clause' }],
      loading: false
    }
  },
  watch: {
    '$route.query': {
      handler(val, oVal) {
        if (val && val.id) {
          this.loadData()
        } else {
          this.$store.dispatch('tagsView/delAllVisitedViews', this.$route)
          this.$router.replace('/index')
        }
      },
      deep: true, // 深度监听
      immediate: true // 如果需要在初次进入时也触发
    }
  },
  methods: {
    loadData() {
      getMasterContractDetail(this.$route.query.id).then(res => {
        this.formData = res.data
      })
    },
    toHash(hash) {
      const target = document.getElementById(hash);
      if (target) {
        target.scrollIntoView({ behavior: 'smooth' }); // 平滑滚动
      }
    },
    toDownload() {
      const link = document.createElement('a');
      link.href = getLocalDownloadUrl(this.formData.filePath);
      link.download = ""; // 设置 download 可以强制浏览器下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    toView() {
      this.$refs.WordModalRef.openModal(this.formData.filePath, this.formData.contractName)

    },
    toTop() {
      const container = document.querySelector('.contract-form ')
      if (container) {
        container.scrollTo({
          top: 0,
          behavior: 'smooth' // 平滑滚动
        });
      }
    },
    goBack() {
      this.$router.back()
    },
    async confirm() {
      const fd = cloneDeep(this.formData)
      delete fd.updatedTime
      delete fd.createdTime
      await updateMasterContract(fd.id, fd)
      Message.success('保存成功')
      this.goBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.contract-form {
  position: relative;
  width: 100%;
  height: 815px;
  box-sizing: border-box;
  padding: 16px;
  overflow: auto;

  .quick-navigate {
    position: fixed;
    bottom: 200px;
    right: 20px;
    width: 120px;
    border-radius: 8px;
    border: 1px solid #ebebeb;
    background: rgba(130, 92, 229, .5);

    .quick-navigate-item {
      width: 100%;
      box-sizing: border-box;
      padding: 10px 0;
      text-align: center;
      color: #fff;
      cursor: pointer;

      &:hover {
        color: #1f97ad;
      }
    }
  }

  .form-back {
    margin-bottom: 20px;
    cursor: pointer;
  }

  .form-block {
    width: 100%;
    box-sizing: border-box;
    padding: 16px 16px 10px;
    border: 1px solid #ebebeb;
    margin-bottom: 20px;

    .form-block-title {
      font-size: 16px;
      font-weight: bold;
    }

    .form-block-main {
      margin-top: 16px;
    }
  }
}
</style>
