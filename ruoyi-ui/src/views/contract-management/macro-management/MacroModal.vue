<template>
  <el-dialog :visible.sync="visible" :title="`${formData.id ? '编辑' : '新增'}`" :fullscreen="formData.valueType === 'FILE'"
    :class="{ 'custom-fullscreen-dialog': formData.valueType === 'FILE' }">
    <el-tabs v-model="activeName">
      <el-tab-pane label="基础信息" name="1">
        <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item prop="name" label="公共值名称">
            <el-input v-model="formData.name" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="归属产品类型">
            <el-select size="small" v-model="formData.productType" clearable placeholder="请选择">
              <el-option label="受托产品" value="受托产品"></el-option>
              <el-option label="组合类产品" value="组合类产品"></el-option>
              <el-option label="货币类产品" value="货币类产品"></el-option>
              <el-option label="债权计划" value="债权计划"></el-option>
              <el-option label="资产支持计划" value="资产支持计划"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属模板分类">
            <el-input v-model="formData.templateCategory" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" placeholder="请输入" clearable type="textarea" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="配置内容" name="2">
        <div v-if="formData.valueType === 'FILE' && visible" class="word-online">
          <WordOnline :url="formData.valueContent" mode="edit" @success="saveCurrent" :key="activeName" />
        </div>
        <el-input v-else v-model="formData.valueContent" type="textarea" :rows="20"></el-input>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm" :loading="btnLoading">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { Message } from 'element-ui'
import { saveCommonValue, updateCommonValue } from '../../../api/contractManagement'
import { getHtml } from '../../../api/onlyoffice'
import WordOnline from '../components/WordOnline.vue'

export default {
  name: 'MacroModal',
  components: {
    WordOnline
  },
  data() {
    return {
      visible: false,
      formData: {
        productType: '',
        templateCategory: '',
        name: '',
        valueContent: '',
        valueType: 'TEXT',
        htmlContent: '',
        remarks: ''
      },
      activeName: "1",
      btnLoading: false,
      fileSaving: false,
      rules: {
        name: [{ required: true, message: '请输入公共值名称' }]
      },
    }
  },
  methods: {
    async confirm() {
      const result = await this.$refs.formRef.validate()
      if (!result) {
        this.activeName = '1'
        return
      }
      if (this.fileSaving) {
        Message.warning('文件保存中,请稍后')
        return
      }
      this.btnLoading = true
      if (this.formData.id) {
        await updateCommonValue(this.formData)
      } else {
        await saveCommonValue(this.formData)
      }
      this.btnLoading = false
      Message.success(`${this.formData.id ? '编辑' : '新增'}成功`)
      this.$emit('success')
      this.close()
    },
    close() {
      this.visible = false
    },
    openModal(row) {
      this.formData = row
      this.activeName = '1'
      this.visible = true
    },
    saveCurrent(fileName) {
      this.fileSaving = true
      getHtml(fileName).then(res => {
        console.log(res)
        this.formData.htmlContent = res.htmlStr
        this.fileSaving = false
      })
      // 在这里保存文件 模板 对应参数
      this.formData.valueContent = fileName
    }
  }
}
</script>

<style lang="scss" scoped>
.word-online {
  height: calc(100vh - 180px);
}
</style>
<style lang="scss">
/* 保证 dialog 内容撑满全屏并内部滚动 */
.custom-fullscreen-dialog {
  .el-dialog {
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin: 0;
    /* 去除默认的 margin */

    .el-dialog__header {
      flex-shrink: 0;
      padding: 20px;
    }

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      padding-top: 0;
      padding-bottom: 10px;
      // padding: 20px;

      /* 可根据需要调整 */
      .dialog-main {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
