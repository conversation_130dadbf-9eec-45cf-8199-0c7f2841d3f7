<template>
  <div class="template-management">
    <div class="search">
      <div class="search-form">
        <el-form :inline="true" :model="query" class="form-inner-error">
          <el-form-item label="公共值名称" style="margin-bottom: 0;">
            <el-input v-model="query.name" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="归属产品类型" style="margin-bottom: 0;">
            <el-select size="small" v-model="query.productType" clearable placeholder="请选择" style="width: 100%;">
              <el-option label="受托产品" value="受托产品"></el-option>
              <el-option label="组合类产品" value="组合类产品"></el-option>
              <el-option label="货币类产品" value="货币类产品"></el-option>
              <el-option label="债权计划" value="债权计划"></el-option>
              <el-option label="资产支持计划" value="资产支持计划"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属模板分类" style="margin-bottom: 0;">
            <el-input v-model="query.templateCategory" placeholder="请输入" clearable />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-action">
        <el-dropdown @command="toCreate">
          <el-button type="primary">新建公共值</el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="TEXT">新建文本公共值</el-dropdown-item>
            <el-dropdown-item command="FILE">新建文件公共值</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" icon="el-icon-search" @click="loadData">查询</el-button>
      </div>
    </div>
    <div class="management-table">
      <el-table :data="records" :loading="loading" row-key="id" stripe>
        <el-table-column type="index" width="50" label="序号"></el-table-column>
        <el-table-column prop="name" label="公共值名称"></el-table-column>
        <el-table-column prop="productType" label="归属产品类型"></el-table-column>
        <el-table-column prop="templateCategory" label="归属模板分类"></el-table-column>
        <el-table-column prop="valueContent" label="公共值内容">
          <template slot-scope="{ row }">
            <el-popover placement="top-start" title="" trigger="hover" v-if="row.valueType === 'FILE'">
              <div class="ellipsis-box" slot="reference" v-html="row.htmlContent"></div>
              <div class="custom-popover" v-html="row.htmlContent"></div>
            </el-popover>
            <span v-else>{{ row.valueContent }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注"></el-table-column>
        <el-table-column prop="creatorName" label="创建人"></el-table-column>
        <el-table-column prop="createdTime" label="创建时间"></el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="{ row }">
            <div class="btn-group">
              <el-button type="text" @click="toEdit(row)">编辑</el-button>
              <el-button type="text" @click="toRemove(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="margin-top: 15px; text-align: right;" :current-page="query.current" :page-size="query.size"
        :total="query.total" layout="prev, pager, next, sizes, total" @current-change="onPageChange"
        @size-change="onSizeChange"></el-pagination>
    </div>
    <MacroModal ref="MacroModalRef" @success="loadData" />
  </div>
</template>

<script>
import { Message } from 'element-ui'
import { getCommonValueList, removeCommonValue } from '../../../api/contractManagement'
import MacroModal from './MacroModal.vue'

export default {
  components: {
    MacroModal
  },
  data() {
    return {
      query: {
        contractName: '', // 模板名称
        productName: '',
        productCode: '',
        current: 1,
        size: 10,
        total: 0
      },
      loading: false,
      records: [],
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    toRemove(row) {
      this.$confirm("确认删除该公共值吗？", "提示").then(() => {
        removeCommonValue(row.id).then(() => {
          Message.success('删除成功')
          this.loadData()
        })
      })
    },
    toEdit(row) {
      this.$refs.MacroModalRef.openModal(row)
    },
    loadData() {
      this.loading = true
      getCommonValueList(this.query).then(res => {
        this.records = res.data.records
        this.query.total = res.data.total
        this.loading = false
      })
    },
    onPageChange(page) {
      this.query.current = page
      this.loadData()
    },
    onSizeChange(size) {
      this.query.size = size
      this.loadData()
    },
    toCreate(valueType) {
      console.log(valueType)
      this.$refs.MacroModalRef.openModal({
        productType: '',
        templateCategory: '',
        name: '',
        valueContent: '',
        valueType,
        htmlContent: '',
        remarks: ''
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.template-management {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px;

  .search {
    width: 100%;
    box-sizing: border-box;
    padding: 16px;
    border: 1px solid #ebebeb;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-form {}

    .search-action {
      display: flex;
      gap: 16px;
    }
  }

  .management-table {
    margin-top: 15px;
    border: 1px solid #ebebeb;
    box-sizing: border-box;
    padding: 16px;
  }

  .btn-group {
    display: flex;
    align-items: center;
    gap: 5px;

    i {
      cursor: pointer;
      color: #825CE4;
      font-size: 18px;
    }
  }
}

.ellipsis-box {
  /* 例如限制为两行 */
  max-height: 100px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* 限制为2行 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}
</style>
