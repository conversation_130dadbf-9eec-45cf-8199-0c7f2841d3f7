<template>
  <div class="template-management">
    <div class="search">
      <div class="search-form">
        <el-form :inline="true" :model="query" class="form-inner-error">
          <el-form-item label="产品名称" style="margin-bottom: 0;">
            <el-input v-model="query.productName" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="合同名称" style="margin-bottom: 0;">
            <el-input v-model="query.contractName" placeholder="请输入" clearable />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-action">
        <el-button type="primary" icon="el-icon-search" @click="loadData">查询</el-button>
      </div>
    </div>
    <div class="management-table">
      <el-table :data="records" :loading="loading" border @row-click="rowClick" stripe fit>
        <el-table-column label="基础信息">
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="产品名称" prop="productName"></el-table-column>
          <el-table-column label="合同名称(在线预览)" prop="contractName">
            <template slot-scope="{row}">
              <el-button type="text" @click.stop="toView(row)">{{ row.contractName }}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="合同下载" prop="contractName">
            <template slot-scope="{row}">
              <el-button type="text" @click.stop="toDownload(row)">{{ row.contractName }}</el-button>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="基础条款">
          <el-table-column label="产品全称" prop="productFullName"></el-table-column>
          <el-table-column label="产品简称" prop="productShortName"></el-table-column>
          <el-table-column label="英文名称" prop="productEnglishName"></el-table-column>
          <el-table-column label="英文简称" prop="productEnglishShortName"></el-table-column>
          <el-table-column label="产品代码" prop="productCode"></el-table-column>
          <el-table-column label="组合代码" prop="portfolioCode"></el-table-column>
          <el-table-column label="资产代码" prop="assetCode"></el-table-column>
        </el-table-column>
        <el-table-column label="账户信息">
          <el-table-column label="托管机构简称" prop="custodianShortName"></el-table-column>
          <el-table-column label="托管机构名称" prop="custodianName"></el-table-column>
          <el-table-column label="托管机构代码" prop="custodianCode"></el-table-column>
          <el-table-column label="托管账户名称" prop="custodianAccountName"></el-table-column>
          <el-table-column label="托管账户户号" prop="custodianAccountNumber"></el-table-column>
          <el-table-column label="开户行" prop="custodianBankName"></el-table-column>
          <el-table-column label="大额支付号" prop="largePaymentNumber"></el-table-column>
        </el-table-column>
        <el-table-column label="合同条款">
          <el-table-column label="合同编号" prop="contractNumber"></el-table-column>
          <el-table-column label="合同名称" prop="contractName"></el-table-column>
          <el-table-column label="合同分类" prop="contractCategory"></el-table-column>
          <el-table-column label="合同状态" prop="contractStatus"></el-table-column>
          <el-table-column label="合同签署日期" prop="signingDate"></el-table-column>
          <el-table-column label="合同生效日期" prop="effectiveDate"></el-table-column>
          <el-table-column label="合同到期日期" prop="expiryDate"></el-table-column>
          <el-table-column label="合同续签日期" prop="renewalDate"></el-table-column>
          <el-table-column label="合同用印形式" prop="sealMethod"></el-table-column>
          <el-table-column label="用印状态" prop="sealStatus"></el-table-column>
        </el-table-column>
      </el-table>
      <el-pagination style="margin-top: 15px; text-align: right;" :current-page="query.page" :page-size="query.pageSize"
        :total="query.total" layout="prev, pager, next, sizes, total" @current-change="onPageChange"
        @size-change="onSizeChange"></el-pagination>
    </div>
    <WordModal ref="WordModalRef" />
  </div>
</template>

<script>
import { getMasterContractList } from '../../../api/contractManagement'
import { getLocalDownloadUrl } from '../../../api/onlyoffice'
import WordModal from '../components/WordModal.vue'


export default {
  name: 'TemplateManagement',
  data() {
    return {
      query: {
        productName: '', // 模板名称
        contractName: '',
        page: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      records: []
    }
  },
  components: {
    WordModal
  },
  mounted() {
    this.loadData()
  },
  activated() {
    this.loadData()
  },
  methods: {
    rowClick(row) {
      this.$router.push({
        path: '/contract-management/contract-form',
        query: {
          id: row.id
        }
      })
    },
    loadData() {
      this.loading = true
      getMasterContractList(this.query).then(res => {
        this.records = res.data.records
        this.query.total = res.data.total
        this.loading = false
      })
    },
    onPageChange(page) {
      this.query.page = page
      this.loadData()
    },
    onSizeChange(size) {
      this.query.pageSize = size
      this.loadData()
    },
    toDownload(row) {
      console.log(row)
      const link = document.createElement('a');
      link.href = getLocalDownloadUrl(row.filePath);
      link.download = ""; // 设置 download 可以强制浏览器下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    toView(row) {
      console.log(this.$refs)
      this.$refs.WordModalRef.openModal(row.filePath, row.contractName)
    },
  }
}
</script>

<style lang="scss" scoped>
.template-management {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px;

  .search {
    width: 100%;
    box-sizing: border-box;
    padding: 16px;
    border: 1px solid #ebebeb;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-form {}
  }

  .management-table {
    margin-top: 15px;
    border: 1px solid #ebebeb;
    box-sizing: border-box;
    padding: 16px;
  }
}
</style>
