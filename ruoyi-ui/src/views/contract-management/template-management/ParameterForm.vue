<template>
  <div class="template-management">
    <div class="search">
      <div class="search-form">
        <el-form :inline="true" :model="query" class="form-inner-error">
          <el-form-item label="参数集名称" style="margin-bottom: 0;">
            <el-input v-model="query.setCode" placeholder="请输入" clearable />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-action">
        <el-button type="primary" icon="el-icon-plus" @click="toParameterModal()">添加</el-button>
        <el-button type="primary" icon="el-icon-search" @click="loadData()">查询</el-button>
      </div>
    </div>
    <div class="management-table">
      <el-table :data="showList" :loading="loading" stripe ref="tableRef">
        <el-table-column type="index" width="50" label="序号" fixed></el-table-column>
        <el-table-column prop="setCode" label="参数集名称" fixed></el-table-column>
        <el-table-column v-for="column in columns" :prop="column" :label="column" :key="column">
          <template slot-scope="{ row }">
            <el-popover placement="top-start" title="" trigger="hover">
              <div class="ellipsis-box" slot="reference" v-html="row.render[column]"></div>
              <div class="custom-popover" v-html="row.render[column]"></div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="{ row }">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="toEdit(row)">编辑</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="toRemove(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <WordModal ref="WordModalRef" />
    <TemplateModal ref="TemplateModalRef" @success="loadData()" />
    <ParameterModal :templateDetail="templateDetail" ref="ParameterModalRef" @success="loadData()"></ParameterModal>
  </div>
</template>

<script>
import { Message } from 'element-ui';
import WordModal from '../components/WordModal.vue';
import TemplateModal from './TemplateModal.vue';
import ParameterModal from './ParameterModal.vue';
import { getParamsList, getTemplateDetail, getTemplateList, removeParams, uploadTemplate } from '../../../api/contractManagement';

export default {
  name: 'TemplateManagement',
  components: {
    WordModal,
    TemplateModal,
    ParameterModal
  },
  computed: {
    showList() {
      return this.records.filter(el => el.setCode.includes(this.query.setCode))
    }
  },
  data() {
    return {
      query: {
        setCode: '', // 模板名称
        page: 1,
        pageSize: 1000,
        total: 0
      },
      columns: [],
      loading: false,
      records: [],
      defaultList: [],
      templateDetail: {}
    }
  },
  watch: {
    '$route.query': {
      handler(val, oVal) {
        if (val && val.id) {
          this.loadData(val.id)
        } else {
          this.$store.dispatch('tagsView/delAllVisitedViews', this.$route)
          this.$router.replace('/index')
        }
      },
      deep: true, // 深度监听
      immediate: true // 如果需要在初次进入时也触发
    }
  },
  methods: {
    toRemove(row) {
      this.$confirm("确认删除该参数集吗？", "提示").then(() => {
        removeParams(row.id).then(() => {
          Message.success('删除成功')
          this.loadData()
        })
      })
    },
    toParameterModal() {
      this.$refs.ParameterModalRef.openModal({
        id: '', templateId: this.$route.query.id, setCode: '', params: this.columns.map(row => {
          return {
            key: row,
            path: '',
            html: '',
            valueType: 'TEXT',
          }
        })
      })
    },
    loadData(id) {
      if (!id) id = this.$route.query.id
      console.log('ididid')
      this.loading = false

      let p1 = new Promise(resolve => {
        getParamsList(id).then(res => {
          this.records = res.data.map(row => {
            const render = {}
            for (let p of row.params) {
              if (p.valueType === 'FILE') {
                render[p.key] = p.html
              } else {
                render[p.key] = p.path
              }
            }
            return {
              ...row,
              render
            }
          })


          console.log(res.data)
          resolve()
        })
      })
      let p2 = new Promise(resolve => {
        getTemplateDetail(id).then(res => {
          this.templateDetail = res.data
          this.defaultList = res.data.detectedParams.map(row => {
            return {
              key: row,
              fileName: ''
            }
          })
          this.columns = res.data.detectedParams
        })
        resolve()
      })
      this.loading = true

      Promise.all([p1, p2]).then(() => {
        this.loading = false
        this.$nextTick(() => {
          setTimeout(() => {
            this.$refs.tableRef && this.$refs.tableRef.doLayout();
          })
        });
      })
    },
    onPageChange(page) {
      this.query.page = page
      this.loadData()
    },
    onSizeChange(size) {
      this.query.pageSize = size
      this.loadData()
    },
    toDownload(row) {
      console.log(row)
      const link = document.createElement('a');
      link.href = row.url;
      link.download = ""; // 设置 download 可以强制浏览器下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    toView(row) {
      console.log(this.$refs)
      this.$refs.WordModalRef.openModal(row.url, row.fileName)
    },
    toEdit(row) {
      this.$refs.ParameterModalRef.openModal({
        id: row.id, templateId: this.$route.query.id, setCode: row.setCode, params: row.params
      })
      // this.$refs.TemplateModalRef.openModal(row)
    },
  }
}
</script>

<style lang="scss" scoped>
.template-management {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px;

  .search {
    width: 100%;
    box-sizing: border-box;
    padding: 16px;
    border: 1px solid #ebebeb;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-form {}

    .search-action {
      display: flex;
      gap: 16px;
    }
  }

  .management-table {
    margin-top: 15px;
    border: 1px solid #ebebeb;
    box-sizing: border-box;
    padding: 16px;

    .ellipsis-box {
      /* 例如限制为两行 */
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      /* 限制为2行 */
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
    }


  }
}
</style>
