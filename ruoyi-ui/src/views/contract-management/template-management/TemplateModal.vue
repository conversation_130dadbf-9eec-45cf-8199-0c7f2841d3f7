<template>
  <el-dialog :visible.sync="visible" :title="`${formData.id ? '编辑' : '新增'}模板`" width="30%">
    <div class="dialog-main" v-loading="loading">
      <div class="dialog-form">
        <el-form :model="formData" label-width="100px" :rules="rules" ref="formRef">
          <el-form-item label="模板id" v-if="formData.id">
            <span>{{ formData.id }}</span>
          </el-form-item>
          <el-form-item prop="templateName" label="模板名称">
            <el-input size="small" v-model="formData.templateName"></el-input>
          </el-form-item>
          <el-form-item prop="productType" label="产品类型">
            <el-select size="small" v-model="formData.productType" clearable placeholder="请选择" style="width: 100%;">
              <el-option label="受托产品" value="受托产品"></el-option>
              <el-option label="组合类产品" value="组合类产品"></el-option>
              <el-option label="货币类产品" value="货币类产品"></el-option>
              <el-option label="债权计划" value="债权计划"></el-option>
              <el-option label="资产支持计划" value="资产支持计划"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="templateCategory" label="模板分类">
            <el-input size="small" v-model="formData.templateCategory"></el-input>
          </el-form-item>
          <template v-if="!formData.id">
            <el-form-item prop="filePath" label="模板附件">
              <el-upload class="upload-demo" drag :limit="1" action="#" :http-request="requestUpload"
                :on-remove="onRemove" :file-list.sync="fileList" :before-upload="beforeUpload" multiple>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </el-upload>
            </el-form-item>
            <div class="param-list" v-if="formData.detectedParams && formData.detectedParams.length > 0">
              <el-collapse>
                <el-collapse-item title="参数列表">
                  <div class="param-main">
                    <div class="param-item" v-for="item in formData.detectedParams">
                      <el-tag>{{ item }}</el-tag>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </template>
          <el-form-item prop="remarks" label="备注">
            <el-input size="small" type="textarea" v-model="formData.remarks"></el-input>
          </el-form-item>
        </el-form>
      </div>


    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确认</el-button>
    </template>

  </el-dialog>
</template>

<script>
import WordOnline from '../components/WordOnline.vue'
import { saveTemplate, saveTemplateParams, uploadTemplate } from '../../../api/contractManagement'
import { Message } from 'element-ui'
import { cloneDeep } from 'lodash'

export default {
  name: 'TemplateModal',
  components: {
    WordOnline
  },
  data() {
    return {
      loading: false,
      visible: false,
      formData: {
        templateName: '',
        productType: '',
        templateCategory: '',
        filePath: '',
        remarks: '',
        detectedParams: []
      },
      fileList: [],
      rules: {
        templateName: [{ required: true, message: '请输入模板名称' }],
        productType: [{ required: true, message: '请输入产品类型' }],
        templateCategory: [{ required: true, message: '请输入模板分类' }],
        filePath: [{ required: true, message: '请上传模板附件' }]
      },
      list: [{ key: '合同名称特长长长长长长长长长长长长长长长长长长长长长长长长长长长长长长', id: 1, fileName: '' }, { key: '合同名称', id: 2, fileName: '' }, { key: '合同名称', id: 3, fileName: '' }
        , { key: '合同名称', id: 4, fileName: '' }, { key: '合同名称', id: 5, fileName: '' }, { key: '合同名称', id: 6, fileName: '' },
      { key: '合同名称', id: 7, fileName: '' }, { key: '合同名称', id: 8, fileName: '' }, { key: '合同名称', id: 9, fileName: '' },
      { key: '合同名称', id: 10, fileName: '' }, { key: '合同名称', id: 11, fileName: '' }, { key: '合同名称', id: 12, fileName: '' },
      { key: '合同名称', id: 13, fileName: '' }, { key: '合同名称', id: 14, fileName: '' }, { key: '合同名称', id: 15, fileName: '' }
      ],
      activeKey: 0,
      activeName: '1'
    }
  },
  mounted() {
    this.changeActive(0)
  },
  methods: {
    requestUpload() {

    },
    onRemove() {
      this.formData.filePath = ''
      this.formData.detectedParams = []
    },
    async beforeUpload(file) {
      console.log("before-upload 被调用");
      console.log(this.fileList)
      if (this.fileList.length >= 1) {
        this.fileList = []; // 清空已有文件列表
      }
      if (!file.name.endsWith('.docx')) {
        Message.warning('仅支持上传docx格式的模板')
      } else {
        const formData = new FormData()
        formData.append('file', file)
        const data = (await uploadTemplate(formData)).data
        this.formData.filePath = data.filePath
        this.formData.detectedParams = data.detectedParams
        console.log(this.formData)
      }
    },
    openModal(row) {
      this.formData = row
      this.visible = true
    },
    changeActive(index) {
      console.log(index)
      this.activeKey = index
    },
    saveCurrent(fileName) {
      // 在这里保存文件 模板 对应参数
      this.list[this.activeKey].fileName = fileName
      console.log(this.list)
    },
    async confirm() {
      const result = await this.$refs.formRef.validate()
      if (!result) return
      const fd = cloneDeep(this.formData)
      if (this.formData.id) fd.templateId = this.formData.id
      console.log(result)
      this.loading = true
      saveTemplate({
        ...fd
      }).then(res => {
        console.log(res.data)
        Message.success(`${this.formData.id ? '编辑' : '新建'}成功`)
        this.visible = false
        this.$emit('success')
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .dialog-form {
    width: 100%;
    margin-top: 20px;

    .dialog-form-line {
      width: 50%;
      display: flex;
      align-items: center;

      .dialog-form-label {
        width: 120px;
      }

      .dialog-form-value {
        flex: 1;
      }
    }

    .param-list {
      margin-bottom: 10px;

      .param-title {}

      .param-main {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        height: 200px;
        overflow: auto;
      }
    }
  }

  .dialog-params {
    height: 710px;
    width: 100%;
    display: flex;
    margin-top: 5px;

    .params-list {
      width: 20%;
      height: 100%;
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .active {
        background: rgba(130, 92, 228, .6);
        color: #fff;
        border: none;
      }

      .params-item {
        width: 100%;
        box-sizing: border-box;
        padding: 16px;
        display: flex;
        border: 1px solid #ebebeb;
        cursor: pointer;
        border-radius: 8px;

        .hidden {
          visibility: hidden;
        }

        .visible {
          visibility: visible;
        }

        .params-item-icon {
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: red;
        }

        .params-item-text {
          width: calc(100% - 6px);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

      }
    }

    .params-word {
      margin-left: 30px;
      width: calc(80% - 30px);
      height: 100%;
    }
  }
}
</style>
<style lang="scss">
/* 保证 dialog 内容撑满全屏并内部滚动 */
.custom-fullscreen-dialog {
  .el-dialog {
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin: 0;
    /* 去除默认的 margin */

    .el-dialog__header {
      flex-shrink: 0;
      padding: 20px;
    }

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      padding-top: 0;
      padding-bottom: 10px;
      // padding: 20px;

      /* 可根据需要调整 */
      .dialog-main {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
