<template>
  <el-dialog :visible.sync="visible" :title="`${formData.id ? '编辑' : '新增'}参数集`" fullscreen
    class="custom-fullscreen-dialog">
    <div class="dialog-main">
      <div class="dialog-form">
        <el-form :model="formData" label-width="100px" :rules="rules" ref="formRef">
          <el-form-item prop="setCode" label="参数集名称" style="margin-bottom: 0;">
            <el-input size="small" v-model="formData.setCode"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-params">
        <div class="params-list">
          <div class="params-list-block" v-for="(item, index) in formData.params" :key="item.key">
            <div :class="`params-item ${activeKey === index ? 'active' : ''}`" :title="item.key"
              @click="changeActive(index)">
              <div :class="`params-item-icon ${item.path ? 'visible' : 'hidden'}`">
              </div>
              <div class="params-item-text">{{ item.key }}</div>
            </div>
            <div class="params-select">
              <el-select v-model="item.valueType" size="small" @change="changeType(index)">
                <el-option label="纯文本" value="TEXT"></el-option>
                <el-option label="文件" value="FILE"></el-option>
              </el-select>
            </div>
          </div>

        </div>
        <div class="params-word" v-if="formData.params[activeKey]">
          <div class="params-action" v-if="showCommonValue.length > 0">
            <el-dropdown split-button type="primary" @command="handleClick">
              添加公共值
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, index) in showCommonValue" :key="item.id" :command="index">{{ item.name
                  }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="params-t" :class="{ full: showCommonValue.length === 0 }">
            <WordOnline v-if="formData.params[activeKey].valueType === 'FILE'" mode="edit"
              :url="formData.params[activeKey].path" @success="saveCurrent"
              :key="activeKey + formData.params[activeKey].valueContent" />
            <template v-else>
              <el-input v-model="formData.params[activeKey].path" type="textarea" :rows="20"></el-input>
            </template>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm" :loading="btnLoading">确认</el-button>
    </template>

  </el-dialog>
</template>

<script>
import { Message } from 'element-ui'
import { getHtml } from '../../../api/onlyoffice'
import WordOnline from '../components/WordOnline.vue'
import { getCommonValueListByQuery, saveTemplateParams } from '../../../api/contractManagement'

export default {
  name: 'ParameterModal',
  components: {
    WordOnline
  },
  props: {
    templateDetail: {
      type: Object
    }
  },
  data() {
    return {
      btnLoading: false,
      visible: false,
      formData: {
        setCode: '',
        templateId: '',
        id: '',
        params: [],
      },
      activeKey: 0,
      rules: {
        setCode: [{ required: true, message: '请输入参数集名称' }]
      },
      loadSet: new Set(),
      commonValueList: []
    }
  },
  computed: {
    showCommonValue() {
      return this.formData.params[this.activeKey] ? this.commonValueList.filter(el => el.valueType === this.formData.params[this.activeKey].valueType) : []
    }
  },
  mounted() {
  },
  methods: {
    changeType(index) {
      this.formData.params[index].path = ''
      if (this.formData.params[index].valueType === '1') {
        this.formData.params[index].html = ''
      }
    },
    handleClick(index) {
      if (this.showCommonValue[index].valueType === 'FILE') {
        this.saveCurrent(this.showCommonValue[index].valueContent)
      } else {
        this.formData.params[this.activeKey].path += this.showCommonValue[index].valueContent
      }
    },
    async confirm() {
      if (this.loadSet.size > 0) {
        Message.warning('部分文件正在保存中，请稍后')
        return
      }
      const result = await this.$refs.formRef.validate()
      if (!result) return
      this.btnLoading = true
      saveTemplateParams(this.formData).then(res => {
        Message.success(`${this.formData.id ? '编辑' : '新增'}成功`)
        this.visible = false
        this.$emit('success')
      }).finally(() => {
        this.btnLoading = false
      })
    },
    openModal(row) {
      this.visible = true
      this.formData = row
      this.loadSet = new Set()
      this.changeActive(0)
      this.loadCommonValue()
    },
    loadCommonValue() {
      if (!this.templateDetail.productType || !this.templateDetail.templateCategory) return
      getCommonValueListByQuery({ productType: this.templateDetail.productType, templateCategory: this.templateDetail.templateCategory }).then(res => {
        this.commonValueList = res.data
      })
    },
    changeActive(index) {
      console.log(index)
      this.activeKey = index
    },
    saveCurrent(fileName) {
      console.log(fileName, this.formData.params)
      this.loadSet.add(fileName)
      const index = this.activeKey
      getHtml(fileName).then(res => {
        console.log(res)
        this.formData.params[index].html = res.htmlStr
        this.loadSet.delete(fileName)
      })
      // 在这里保存文件 模板 对应参数
      this.formData.params[index].path = fileName
      console.log(this.list)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .dialog-form {
    height: 40px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    .dialog-form-line {
      width: 100%;
      display: flex;
      align-items: center;

      .dialog-form-label {
        width: 120px;
      }

      .dialog-form-value {
        flex: 1;
      }
    }
  }

  .dialog-params {
    height: calc(100% - 45px);
    width: 100%;
    display: flex;
    margin-top: 15px;

    .params-list {
      width: 30%;
      height: 100%;
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .params-list-block {
        box-sizing: border-box;
        padding: 0 10px;
        width: 100%;
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .active {
        background: rgba(130, 92, 228, .6);
        color: #fff;
        border: none;
      }

      .params-item {
        width: 60%;
        box-sizing: border-box;
        padding: 6px 16px;
        display: flex;
        border: 1px solid #ebebeb;
        cursor: pointer;
        border-radius: 8px;

        .hidden {
          visibility: hidden;
        }

        .visible {
          visibility: visible;
        }

        .params-item-icon {
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: red;
        }

        .params-item-text {
          width: calc(100% - 6px);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

      }
    }

    .params-word {
      margin-left: 30px;
      width: calc(70% - 30px);
      height: 100%;

      .params-action {
        height: 32px;
        margin-bottom: 8px;
      }

      .params-t {
        height: calc(100% - 40px);
      }

      .full {
        height: 100%;
      }
    }
  }
}
</style>
<style lang="scss">
/* 保证 dialog 内容撑满全屏并内部滚动 */
.custom-fullscreen-dialog {
  .el-dialog {
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin: 0;
    /* 去除默认的 margin */

    .el-dialog__header {
      flex-shrink: 0;
      padding: 20px;
    }

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      padding-top: 0;
      padding-bottom: 10px;
      // padding: 20px;

      /* 可根据需要调整 */
      .dialog-main {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
