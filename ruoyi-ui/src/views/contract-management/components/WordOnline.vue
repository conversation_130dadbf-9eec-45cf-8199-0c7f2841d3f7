<template>
  <div id="onlyoffice-container">
    <div id="editor"></div>
  </div>
</template>

<script>
import { nanoid } from 'nanoid';
import { getDefaultDoc, getDownloadUrl, getFileName, getLoadUrl, getOnlyOfficeAutoSave, quickUpload } from '../../../api/onlyoffice';
import { KJUR } from 'jsrsasign';


export default {
  name: "WordOnline",
  props: {
    mode: {
      type: String,
      default: 'view'
    },
    fileName: {
      type: String,
      required: false,
    },
    url: {
      type: String,
      required: false,
    },
    comparePath: {
      type: String
    }
  },
  mounted() {
    this.initEditor();
  },
  data() {
    return {
      saving: false
    }
  },
  beforeDestroy() {
    if (this.editor) this.editor.destroyEditor()
  },
  methods: {
    getSaveStatus() {
      return this.saving
    },
    // 有三种模式 纯看 对比 编辑
    async initEditor() {

      const url = this.url ? getDownloadUrl(this.url) : getDefaultDoc()
      const title = this.fileName ? this.fileName : (this.url ? getFileName(this.url) : '新建文档')
      const key = new Date().getTime().toString();
      console.log(getDownloadUrl(this.url), url)


      console.log(token, 'token')
      let config;
      console.log(this)
      if (this.mode === 'edit') {
        console.log('load')
        config = {
          document: {
            fileType: "docx",
            key,
            title,
            url,
          },
          documentType: "word",
          mode: this.mode,
          editorConfig: {
            plugins: {
              autostart: ["asc.{0616AE85-5DBE-4B6B-A0A9-455C4F1503AD}"],
              // pluginsData: [process.env.NODE_ENV === "development" ? getLoadUrl('plugins/config.json') : `http://${window.location.hostname}:3000/loadFile/plugins/config.json`],
              pluginsData: [process.env.NODE_ENV === "development" ? getLoadUrl('plugins/config.json') : `http://***********:3002/loadFile/plugins/config.json`],
            },
            mode: this.mode,
            review: false,
            callbackUrl: getOnlyOfficeAutoSave(),
            lang: "zh",
            user: {
              id: key,
              name: this.$store.state.user.name,
            },
            customization: {
              features: {
                spellcheck: false,
              },
              forcesave: true,
              autosave: false,
              review: {
                hideReviewDisplay: false,
                showReviewChanges: false,
                reviewDisplay: false,
                trackChanges: false,
                hoverMode: false,
              }
            },
          },
          events: {
            onDocumentReady: () => {
              console.log("Loaded");
              this.editor.executeCommand("autoAccept", []);

            },
            onError: (err) => console.error("OnlyOffice Error:", err),
            onSaveDocument: async (r) => {
              function arrayBufferToFile(
                buffer,
                fileName,
                mimeType = "application/octet-stream"
              ) {
                const blob = new Blob([buffer], { type: mimeType });
                return new File([blob], fileName, { type: mimeType });
              }
              const randomId = nanoid()
              const file = arrayBufferToFile(
                r.data,
                `${randomId}.docx`, // 模板名
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              );
              const formData = new FormData()
              formData.append('file', file);
              formData.append('filepath', `${randomId}.docx`)
              this.saving = true
              await fetch(quickUpload(), {
                method: "POST",
                body: formData,
              })
              this.saving = false
              this.$emit('success', `${randomId}.docx`)
              console.log('onSaveDocument')
              // 模板那里保存的时候 我出去保存吧 r.data是file
            }
          },
        };
      }
      else if (!this.comparePath) {
        config = {
          document: {
            fileType: "docx",
            key: new Date().getTime().toString(),
            title: this.fileName || getFileName(this.url),
            url,
            permissions: {
              review: false
            }
          },
          documentType: "word",
          editorConfig: {
            mode: this.mode,
            callbackUrl: getOnlyOfficeAutoSave(),
            lang: "zh",
            user: {
              id: key,
              name: this.$store.state.user.name,
            },
            customization: {
              notifications: false,
              layout: {
                toolbar: {
                  collaboration: {
                    mailmerge: false,
                  },
                },
              },
              forcesave: true,
              autosave: false,
              review: {
                hideReviewDisplay: true,
                showReviewChanges: true,
                reviewDisplay: 'final',
                trackChanges: false,
                hoverMode: true,
              },
            },
          },
          events: {
            onDocumentReady: () => {
              console.log("Loaded");
            },
            onError: (err) => console.error("OnlyOffice Error:", err),
            onSaveDocument: async (r) => {
            }
          },
        };
      } else {
        config = {
          document: {
            fileType: "docx",
            key,
            title,
            url,
            permissions: {
              review: true,
              edit: false
            }
          },

          documentType: "word",
          editorConfig: {
            mode: 'edit',
            callbackUrl: getOnlyOfficeAutoSave(),
            lang: "zh",
            user: {
              id: key,
              name: this.$store.state.user.name,
            },
            coEditing: { mode: "none" },
            customization: {
              notifications: false,
              layout: {
                toolbar: {
                  collaboration: {
                    mailmerge: false,
                  },
                },
              },
              forcesave: true,
              autosave: false,
              review: {
                trackChanges: true,
                reviewDisplay: "markup",
                showReviewChanges: true,
                hideReviewDisplay: true,
                hoverMode: false
              },
            },
          },
          events: {
            onDocumentReady: () => {
              console.log("Loaded");
              if (this.comparePath)
                this.compareDocuments();
            },
            onError: (err) => console.error("OnlyOffice Error:", err),
            onSaveDocument: async (r) => {
            }
          },
        };
      }
      const header = { alg: 'HS256', typ: 'JWT' };

      // 2. 用 jsrsasign 生成 token
      //    第四个参数是 secret（对称密钥），一定要和 OnlyOffice 配置里 secret.inbox.string 一致
      const SECRET = '********************************';
      const sHeader = JSON.stringify(header);
      const sPayload = JSON.stringify(config);
      const token = KJUR.jws.JWS.sign('HS256', sHeader, sPayload, SECRET);
      config = Object.assign({}, config, { token });
      console.log(config)

      this.editor = new DocsAPI.DocEditor("editor", config);
    },
    compareDocuments() {
      if (!this.editor) return;
      console.log(getDownloadUrl(this.comparePath))

      this.editor.setRequestedDocument({
        c: "compare",
        url: getDownloadUrl(this.comparePath),
        fileType: "docx",
      });
    },
  },
};
</script>

<style scoped>
#onlyoffice-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

#editor {
  width: 100%;
  height: 100%;
}
</style>
