<template>
  <el-dialog :visible.sync="visible" fullscreen class="custom-fullscreen-dialog" destroy-on-close :modal="false"
    @close="close">
    <div class="dialog-main">
      <WordOnline v-if="url" :url="url" :fileName="fileName" mode="edit" ref="WordOnlineRef" @success="success" />
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import WordOnline from './WordOnline.vue'
import { saveContractFile } from '../../../api/contractManagement'
import { Message } from 'element-ui'

export default {
  name: 'WordEditModal',
  components: {
    WordOnline
  },
  data() {
    return {
      visible: false,
      url: '',
      fileName: '',
      contractId: '',
      filePath: ''
    }
  },
  methods: {
    openModal(url, fileName, contractId) {
      console.log(fileName)
      this.url = url
      this.fileName = fileName
      this.contractId = contractId
      this.visible = true
    },
    close() {
      this.$emit('close')
    },
    success(filePath) {
      this.filePath = filePath
    },
    confirm() {
      if (this.$refs.WordOnlineRef.getSaveStatus()) {
        Message.warning('文件保存中，请稍后')
        return
      }
      saveContractFile(this.contractId, this.filePath).then(() => {
        Message.success('保存成功')
        this.visible = false
        this.$emit('success')
      })
    }
  }
}
</script>

<style lang="scss">
/* 保证 dialog 内容撑满全屏并内部滚动 */
.custom-fullscreen-dialog {
  .el-dialog {
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin: 0;
    /* 去除默认的 margin */

    .el-dialog__header {
      flex-shrink: 0;
      padding: 20px;
    }

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      padding-top: 0;
      padding-bottom: 10px;
      // padding: 20px;

      /* 可根据需要调整 */
      .dialog-main {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
