<template>
  <el-dialog :visible.sync="visible" fullscreen class="custom-fullscreen-dialog" destroy-on-close :modal="false"
    @close="close">
    <div class="dialog-main">
      <WordOnline v-if="url" :url="url" :fileName="fileName" :comparePath="comparePath" mode="view" :key="key"
        @success="success" />
    </div>
  </el-dialog>
</template>

<script>
import WordOnline from './WordOnline.vue'

export default {
  name: 'WordModal',
  components: {
    WordOnline
  },
  data() {
    return {
      visible: false,
      url: '',
      fileName: '',
      comparePath: '',
      key: ''
    }
  },
  mounted() {
    this.key = new Date().getTime().toString()
  },
  methods: {
    openModal(url, fileName, comparePath) {
      this.key = new Date().getTime().toString()
      console.log(fileName)
      this.url = url
      this.fileName = fileName
      this.comparePath = comparePath
      this.visible = true
    },
    close() {
      this.$emit('close')
    },
    success(filePath) {
      this.$emit('success', filePath)
    }
  }
}
</script>

<style lang="scss">
/* 保证 dialog 内容撑满全屏并内部滚动 */
.custom-fullscreen-dialog {
  .el-dialog {
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin: 0;
    /* 去除默认的 margin */

    .el-dialog__header {
      flex-shrink: 0;
      padding: 20px;
    }

    .el-dialog__body {
      flex: 1;
      overflow: auto;
      padding-top: 0;
      padding-bottom: 10px;
      // padding: 20px;

      /* 可根据需要调整 */
      .dialog-main {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
