<template>
  <el-dialog :visible.sync="visible" title="文件对比" fullscreen append-to-body>
    <div class="doc-compare-split" style="display: flex; height: 100vh;">
      <!-- 左侧：旧版本文档 -->
      <div id="onlyoffice-left-parent" style="flex: 1; border-right: 1px solid #ddd;">
        <div id="onlyoffice-left"></div>
      </div>
      <!-- 右侧：新版本文档 -->
      <div id="onlyoffice-right-parent" style="flex: 1;">
        <div id="onlyoffice-right"></div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { nanoid } from 'nanoid';
import { getDefaultDoc, getDownloadUrl, getLoadUrl, getOnlyOfficeAutoSave } from '../../../api/onlyoffice';

export default {
  name: 'WordCompareModal',
  data() {
    return {
      editorLeft: null,
      editorRight: null,
      visible: false,
      leftKey: '',
      rightKey: '',
      leftIframe: null,
      rightIframe: null
    };
  },
  methods: {
    openModal(url, comparePath) {
      this.visible = true;
      // 唯一标识，用于插件之间区分
      this.leftKey = nanoid();
      this.rightKey = nanoid();
      this.$nextTick(() => {
        this.initLeft(url, comparePath);
        this.initRight(url);
        this.pollIframes();
        // 监听滚动消息
        window.addEventListener('message', this.handleMessage, false);
      });
    },
    initLeft(currentUrl, comparePath) {
      const url = currentUrl ? getDownloadUrl(currentUrl) : getDefaultDoc();
      const key = this.leftKey;
      const config = {
        document: { fileType: 'docx', key, title: '旧版本文档', url, permissions: { review: false } },
        documentType: 'word',
        editorConfig: {
          // 保留原有plugins配置，注入滚动同步插件
          plugins: {
            autostart: ["asc.{0616AE85-5DBE-4B6B-A0A9-455C4F1503AD}"],
            pluginsData: [process.env.NODE_ENV === "development" ? getLoadUrl('plugins/config.json') : `http://${window.location.hostname}:3000/loadFile/plugins/config.json`],
          },
          // ...其他editorConfig保持不变
          mode: 'view',
          callbackUrl: getOnlyOfficeAutoSave(),
          lang: 'zh',
          customization: { /* ... */ }
        },
        events: { onDocumentReady: () => this.compareDocuments(comparePath) }
      };
      this.editorLeft = new DocsAPI.DocEditor('onlyoffice-left', config);
    },
    initRight(currentUrl) {
      const url = currentUrl ? getDownloadUrl(currentUrl) : getDefaultDoc();
      const key = this.rightKey;
      const config = {
        document: { fileType: 'docx', key, title: '新版本文档', url, permissions: { review: false } },
        documentType: 'word',
        editorConfig: {
          plugins: {
            autostart: ["asc.{0616AE85-5DBE-4B6B-A0A9-455C4F1503AD}"],
            pluginsData: [process.env.NODE_ENV === "development" ? getLoadUrl('plugins/config.json') : `http://${window.location.hostname}:3000/loadFile/plugins/config.json`],
          },
          mode: 'view',
          callbackUrl: getOnlyOfficeAutoSave(),
          lang: 'zh',
          customization: { /* ... */ }
        },
        events: { onDocumentReady: () => {/* 无对比操作 */ } }
      };
      this.editorRight = new DocsAPI.DocEditor('onlyoffice-right', config);
    },
    compareDocuments(comparePath) {
      this.editorLeft &&
        this.editorLeft.setRequestedDocument({ c: 'compare', url: getDownloadUrl(comparePath), fileType: 'docx' });
    },
    pollIframes() {
      const poll = setInterval(() => {
        const left = document.querySelector('#onlyoffice-left-parent iframe');
        const right = document.querySelector('#onlyoffice-right-parent iframe');
        if (left && right) {
          clearInterval(poll);
          this.leftIframe = left;
          this.rightIframe = right;
        }
      }, 200);
    },
    handleMessage(event) {
      console.log(event, 'message')
      const msg = event.data;
      if (msg && msg.type === 'sync-scroll') {
        // 根据key转发给另一侧iframe
        if (msg.key === this.leftKey && this.rightIframe) {
          this.rightIframe.contentWindow.postMessage(msg, '*');
        } else if (msg.key === this.rightKey && this.leftIframe) {
          this.leftIframe.contentWindow.postMessage(msg, '*');
        }
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage, false);
    this.editorLeft && this.editorLeft.destroyEditor();
    this.editorRight && this.editorRight.destroyEditor();
  }
};
</script>

<style scoped>
/* 自定义滚动条样式，如需 */
</style>
