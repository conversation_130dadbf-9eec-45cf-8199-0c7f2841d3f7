<template>
  <el-dialog :visible.sync="visible" title="版本历史" width="30%" :modal="modalVisible">
    <div class="timeline">
      <el-timeline :reverse="false">
        <el-timeline-item v-for="(item, index) in list" :key="index" :timestamp="item.editedTime">
          <el-card>
            <div class="timeline-item">
              <div class="timeline-item-info">
                <span>{{ item.version }}</span>
                <span>{{ item.editedName }}</span>
              </div>
              <div class="timeline-item-action">
                <template v-if="index > 0">
                  <el-button size="mini" type="text" icon="el-icon-finished" @click="toCompare(item)">对比当前版本</el-button>
                  <el-button size="mini" type="text" icon="el-icon-download" @click="toDownload(item)">下载</el-button>
                </template>
                <el-upload action="#" :http-request="requestUpload" :show-file-list="false"
                  :before-upload="(file) => { beforeUpload(file, item) }">
                  <el-button size="mini" type="text" icon="el-icon-upload2" :loading="saving">上传文件对比</el-button>
                </el-upload>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
    <WordModal ref="WordModalRef" @close="close" />
    <WordCompareModal ref="WordCompareModalRef" @close="close" />
  </el-dialog>
</template>

<script>
import dayjs from 'dayjs'
import { getContractVersions } from '../../api/contractManagement'
import WordModal from './components/WordModal.vue'
import { getLocalDownloadUrl, quickUpload } from '../../api/onlyoffice'
import { nanoid } from 'nanoid'
import WordCompareModal from './components/WordCompareModal.vue'

export default {
  name: 'HistoryModal',
  components: {
    WordModal,
    WordCompareModal
  },
  data() {
    return {
      visible: false,
      list: [],
      filepath: '',
      modalVisible: true,
      saving: false
    }
  },
  methods: {
    requestUpload() {

    },
    async beforeUpload(file, item) {
      console.log(file, item)
      const randomId = nanoid()
      const formData = new FormData()
      formData.append('file', file);
      formData.append('filepath', `${randomId}.docx`)
      this.saving = true
      fetch(quickUpload(), {
        method: "POST",
        body: formData,
      }).then(() => {
        console.log(item.storagePath, `${randomId}.docx`)
        this.$refs.WordModalRef.openModal(`${randomId}.docx`, '合同对比', item.storagePath)
        this.modalVisible = false
      }).finally(() => {
        this.saving = false
      })
    },
    close() {
      this.modalVisible = true
    },
    openModal(id, filepath) {
      getContractVersions(id).then(res => {
        this.list = res.data.map(row => {
          return {
            ...row,
            editedTime: dayjs(row.editedTime).format('YYYY-MM-DD hh:mm:ss')
          }
        })
        this.visible = true
      })
      this.filepath = filepath
      // 用这个id获取历史版本 然后最新的那个不带操作按钮
    },
    toCompare(row) {
      console.log(row.storagePath, this.filepath)
      this.$refs.WordModalRef.openModal(row.storagePath, '合同对比', this.filepath)
      // this.$refs.WordCompareModalRef.openModal(row.storagePath, this.filepath)
      this.modalVisible = false

    },
    toDownload(row) {
      const link = document.createElement('a');
      link.href = getLocalDownloadUrl(row.storagePath);
      link.download = ""; // 设置 download 可以强制浏览器下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}
</script>

<style lang="scss" scoped>
.timeline {
  max-height: 550px;
  overflow: auto;
  box-sizing: border-box;
  padding-right: 10px;

  .timeline-item {
    .timeline-item-info {
      display: flex;
      align-items: center;
      font-weight: bold;
      gap: 6px;
      margin-bottom: 6px;
    }

    span {
      margin-right: 6px;
    }

    .timeline-item-action {
      display: flex;
      align-items: center;
      gap: 16px;

      .el-button+.el-button {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
