<template>
  <div class="template-management">
    <div class="search">
      <div class="search-form">
        <el-form :inline="true" :model="query" class="form-inner-error">
          <el-form-item label="合同名称" style="margin-bottom: 0;">
            <el-input v-model="query.contractName" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="产品名称" style="margin-bottom: 0;">
            <el-input v-model="query.productName" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="产品代码" style="margin-bottom: 0;">
            <el-input v-model="query.productCode" placeholder="请输入" clearable />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-action">
        <el-dropdown @command="toCreate">
          <el-button type="primary">发起合同</el-button>

          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1">发起托管合同</el-dropdown-item>
            <el-dropdown-item command="2">发起产品合同</el-dropdown-item>
            <el-dropdown-item command="3">发起清盘合同</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" icon="el-icon-search" @click="loadData">查询</el-button>
      </div>
    </div>
    <div class="management-table">
      <el-table :data="records" :loading="loading" row-key="id" stripe>
        <el-table-column type="index" width="50" label="序号"></el-table-column>
        <el-table-column prop="contractName" label="合同名称"></el-table-column>
        <el-table-column prop="productCode" label="产品代码"></el-table-column>
        <el-table-column prop="productName" label="产品名称"></el-table-column>
        <el-table-column prop="productType" label="产品类型"></el-table-column>
        <el-table-column prop="status" label="处理状态"></el-table-column>
        <el-table-column prop="investmentManager" label="投资经理">
          <template slot-scope="{row}">
            {{userList.find(el => el.value === row.investmentManager) ? userList.find(el => el.value ===
              row.investmentManager).label : row.investmentManager}}
          </template>
        </el-table-column>
        <el-table-column prop="productManager" label="产品经理">
          <template slot-scope="{row}">
            {{userList.find(el => el.value === row.productManager) ? userList.find(el => el.value ===
              row.productManager).label : row.productManager}}
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="创建人"></el-table-column>
        <el-table-column prop="createdTime" label="创建时间"></el-table-column>
        <el-table-column prop="name" label="版本历史">
          <template slot-scope="{ row }">
            <el-button type="text" @click="toHistoryModal(row)">{{ row.version }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template slot-scope="{ row }">
            <div class="btn-group">
              <i class="el-icon-view" title="预览" @click="toView(row)"></i>
              <i class="el-icon-edit" title="编辑合同信息" @click="toEdit('1', row)"></i>
              <i class="el-icon-edit-outline" title="编辑合同文件" @click="toEdit('2', row)"></i>
              <i class="el-icon-download" title="下载" @click="toDownload(row)"></i>
              <i class="el-icon-document-checked" v-if="row.status !== '审核通过'" style="color: #67C23A;" title="审核通过"
                @click="toComplete(row)"></i>
              <i class="el-icon-delete" style="color: #F56C6C;" title="删除" @click="toRemove(row)"></i>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="margin-top: 15px; text-align: right;" :current-page="query.page" :page-size="query.pageSize"
        :total="query.total" layout="prev, pager, next, sizes, total" @current-change="onPageChange"
        @size-change="onSizeChange"></el-pagination>
    </div>
    <ContractModal ref="ContractModalRef" />
    <HistoryModal ref="HistoryModalRef" />
    <WordEditModal ref="WordEditModalRef" @success="loadData" />
    <WordModal ref="WordModalRef" />
  </div>
</template>

<script>
import { Message } from 'element-ui'
import { completeContract, getContractDetail, getContractList, removeContract } from '../../api/contractManagement'
import ContractModal from './ContractModal.vue'
import HistoryModal from './HistoryModal.vue'
import WordEditModal from './components/WordEditModal.vue'
import WordModal from './components/WordModal.vue'
import { getLocalDownloadUrl } from '../../api/onlyoffice'
import { listUser } from '../../api/system/user'


export default {
  components: {
    ContractModal,
    HistoryModal,
    WordEditModal,
    WordModal
  },
  data() {
    return {
      query: {
        contractName: '', // 模板名称
        productName: '',
        productCode: '',
        page: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      records: [],
      userList: []
    }
  },
  mounted() {
    this.loadData()
    this.loadUserList()
  },
  methods: {
    loadUserList() {
      listUser({ pageNum: 1, pageSize: 99999 }).then(res => {
        this.userList = res.rows.map(row => {
          return {
            label: row.nickName,
            value: row.userId
          }
        })
      })
    },
    toHistoryModal(row) {
      this.$refs.HistoryModalRef.openModal(row.id, row.filePath)
    },
    toRemove(row) {
      this.$confirm("确认删除该合同吗？", "提示").then(() => {
        removeContract(row.id).then(() => {
          Message.success('删除成功')
          this.loadData()
        })
      })
    },
    async toComplete(row) {
      await completeContract(row.id)
      Message.success('审核通过')
      this.loadData()
    },
    toEdit(s, row) {
      if (s === '1') {
        getContractDetail(row.id)
        this.$refs.ContractModalRef.openModal({
          id: row.id,
          contractName: row.contractName,
          templateId: row.templateId,
          productCode: row.productCode,
          productName: row.productName,
          productType: row.productType,
          investmentManager: row.investmentManager,
          productManager: row.productManager,
          parameterSetId: row.parameterSetId
        })
      } else {
        console.log(this.$refs)
        this.$refs.WordEditModalRef.openModal(row.filePath, row.contractName, row.id)
      }
    },
    loadData() {
      this.loading = true
      getContractList(this.query).then(res => {
        this.records = res.data.records
        this.query.total = res.data.total
        this.loading = false
      })
    },
    onPageChange(page) {
      this.query.page = page
      this.loadData()
    },
    onSizeChange(size) {
      this.query.pageSize = size
      this.loadData()
    },
    toCreate(command) {
      this.$refs.ContractModalRef.openModal({
        contractName: '',
        templateId: "",
        productCode: "",
        productName: "",
        productType: "",
        investmentManager: "",
        productManager: "",
        parameterSetId: ""
      })
      console.log(command)
    },
    toDownload(row) {
      console.log(row)
      const link = document.createElement('a');
      link.href = getLocalDownloadUrl(row.filePath, row.contractName);
      console.log(link.href)
      link.download = ""; // 设置 download 可以强制浏览器下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    toView(row) {
      console.log(this.$refs)
      this.$refs.WordModalRef.openModal(row.filePath, row.contractName)
    },
  }
}
</script>

<style lang="scss" scoped>
.template-management {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px;

  .search {
    width: 100%;
    box-sizing: border-box;
    padding: 16px;
    border: 1px solid #ebebeb;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-form {}

    .search-action {
      display: flex;
      gap: 16px;
    }
  }

  .management-table {
    margin-top: 15px;
    border: 1px solid #ebebeb;
    box-sizing: border-box;
    padding: 16px;
  }

  .btn-group {
    display: flex;
    align-items: center;
    gap: 5px;

    i {
      cursor: pointer;
      color: #825CE4;
      font-size: 18px;
    }
  }
}
</style>
