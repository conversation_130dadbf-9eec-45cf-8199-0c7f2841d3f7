<template>
  <el-form ref="formRef" :model="formData" :rules="formRules" label-width="140px">
    <el-row>
      <el-col :span="12" v-if="!batchEdit">
        <el-form-item :label="taskNameLabel" prop="taskName">
          <el-input v-model="formData.taskName" clearable placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="任务类型" prop="taskType">
          <el-select style="width:100%" v-model="formData.taskType" placeholder="请选择"
            :disabled="(isTempTask && !isLeader) || formData.taskTriggerType === 'dynamic'"
            @change="afterTaskTypeChange">
            <el-option label="日常任务" value="daily" />
            <el-option label="周期任务" value="period" />
            <el-option label="临时待办" value="temp" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row v-if="formData.taskType !== 'temp'">
      <el-col :span="12">
        <el-form-item label="任务触发类型" prop="taskTriggerType">
          <el-select style="width:100%" v-model="formData.taskTriggerType" placeholder="请选择"
            @change="taskTriggerTypeChange">
            <el-option label="立即生成" value="manual" />
            <el-option v-if="!isCreate" label="工作日" value="daily" />
            <!-- <el-option v-if="!isCreate" label="自定义" value="dynamic" /> -->
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row v-if="formData.taskTriggerType === 'dynamic'">
      <el-col :span="12">
        <el-form-item label="触发器" prop="taskTriggerId">
          <el-select style="width:100%" v-model="formData.taskTriggerId" placeholder="请选择" filterable>
            <el-option v-for="item in tradeTypeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="触发频率" prop="taskCronVal">
          <el-select style="width:100%" v-model="formData.taskCronVal" placeholder="请选择">
            <el-option label="1小时" value="1" />
            <el-option label="2小时" value="2" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="任务描述" prop="taskDesc" v-if="!batchEdit">
      <el-input v-model="formData.taskDesc" type="textarea" clearable placeholder="请输入" />
    </el-form-item>

    <el-divider></el-divider>

    <el-row>
      <el-col :span="12">
        <el-form-item label="开始时间" prop="taskStartTime">
          <el-date-picker v-model="formData.taskStartTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择" :picker-options="{ selectableRange: '00:00:00 - 23:59:59' }" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="截止时间" prop="taskEndTime">
          <el-date-picker v-model="formData.taskEndTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择" :picker-options="{ selectableRange: '00:00:00 - 23:59:59' }" />
        </el-form-item>
      </el-col>
    </el-row>
    <template>
      <div v-if="!isWorkbenchTemp">
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否需要附件" prop="taskAttachmentsType">
              <el-radio-group v-model="formData.taskAttachmentsType">
                <el-radio :label="'0'">否</el-radio>
                <el-radio :label="'1'">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务告警通知方式" prop="taskWarnNotice">
              <el-select style="width:100%" v-model="formData.taskWarnNotice" placeholder="请选择" :disabled="true">
                <el-option label="站内" value="web" />
                <el-option label="邮件" value="mail" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="任务自动计数" prop="taskAuditType">
              <el-radio-group v-model="formData.taskAuditType" :disabled="isWorkbenchTemp">
                <el-radio :label="'0'">否</el-radio>
                <el-radio :label="'1'">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col v-if="formData.taskAuditType === '1'" :span="12">
            <el-form-item label="自动计数监控单元" prop="taskAuditUnitId">
              <el-select style="width:100%" v-model="formData.taskAuditUnitId" placeholder="请选择" filterable>
                <el-option v-for="item in tradeTypeList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </template>


    <el-row>
      <el-col :span="12">
        <el-form-item label="任务优先级" prop="taskPriority">
          <el-radio-group v-model="formData.taskPriority">
            <el-radio-button label="1">高</el-radio-button>
            <el-radio-button label="2">中</el-radio-button>
            <el-radio-button label="3">低</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="任务紧急程度" prop="taskLevel">
          <el-radio-group v-model="formData.taskLevel">
            <el-radio-button label="1">紧急</el-radio-button>
            <el-radio-button label="2">一般</el-radio-button>
            <el-radio-button label="3">普通</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12">
        <el-form-item label="任务归属" prop="taskOwnerType">
          <el-select style="width:100%" v-model="formData.taskOwnerType" placeholder="请选择"
            @change="afterTaskOwnerTypeChange">
            <el-option label="组织" value="1" />
            <el-option label="人员" value="2" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12">
        <el-form-item label="归属组织" prop="ownerOrgId">
          <ElTreeSelect v-model="formData.ownerOrgId" :options="orgTree"
            :props="{ label: 'orgName', children: 'children', checkStrictly: true, value: 'id', emitPath: false }"
            placeholder="请选择" @change="afterOwnerOrgIdChange" />

        </el-form-item>
      </el-col>
      <el-col v-if="formData.taskOwnerType === '2'" :span="12">
        <el-form-item label="责任人" prop="taskOwnerId">
          <el-select style="width:100%" v-model="formData.taskOwnerId" :disabled="!formData.ownerOrgId"
            :placeholder="'请选择'">
            <el-option v-for="item in ownerUserList" :key="item.id" :label="item.orgName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- <el-row v-if="isTemp">
      <el-col :span="12">
        <el-form-item label="是否包含明细" prop="importStatus">
          <el-radio-group v-model="formData.importStatus">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row> -->


    <el-row>
      <el-col :span="12">
        <el-form-item label="是否需要复核" prop="taskCheckReq">
          <el-radio-group v-model="formData.taskCheckReq" @change="taskCheckReqChange">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row v-if="formData.taskCheckReq === '1'">
      <el-col :span="12">
        <el-form-item label="复核归属" prop="taskCheckType">
          <el-select style="width:100%" v-model="formData.taskCheckType" placeholder="请选择"
            @change="afterTaskCheckTypeChange">
            <el-option label="组织" value="1" />
            <el-option label="人员" value="2" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row v-if="formData.taskCheckReq === '1'">
      <el-col :span="12">
        <el-form-item label="复核岗" prop="checkOrgId">
          <ElTreeSelect v-model="formData.checkOrgId" :options="orgTree"
            :props="{ label: 'orgName', children: 'children', checkStrictly: true, value: 'id', emitPath: false }"
            placeholder="请选择" @change="afterCheckOrgIdChange" />

        </el-form-item>
      </el-col>
      <el-col v-if="formData.taskCheckType === '2'" :span="12">
        <el-form-item label="复核人" prop="taskCheckId">
          <el-select style="width:100%" v-model="formData.taskCheckId" :disabled="!formData.checkOrgId"
            placeholder="请选择">
            <el-option v-for="item in checkUserList" :key="item.id" :label="item.orgName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <template v-if="!isWorkbenchTemp">
      <el-row v-if="formData.taskType !== 'period'">
        <el-col :span="12">
          <el-form-item label="是否可以顺延" prop="taskDeferredType">
            <el-radio-group v-model="formData.taskDeferredType">
              <el-radio label="0">否</el-radio>
              <el-radio label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="formData.taskDeferredType === '1'">
          <el-form-item label="顺延几个工作日" prop="taskDeferredCount">
            <el-input-number v-model="formData.taskDeferredCount" :min="0" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="是否开启统计" prop="workAmountFlag">
            <el-radio-group v-model="formData.workAmountFlag">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- <el-form-item label="是否开启内容解析" prop="contentParse">
            <el-radio-group v-model="formData.contentParse">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item> -->
        </el-col>
        <el-col :span="12" v-if="formData.workAmountFlag === 1">
          <el-form-item label="任务统计量值" prop="workAmount">
            <el-input-number v-model="formData.workAmount" :min="0" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="isLeader">
        <el-col :span="12">
          <el-form-item label="是否开启特殊任务统计" prop="taskReportFlag">
            <el-radio-group v-model="formData.taskReportFlag">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="formData.taskReportFlag === 1">
          <el-form-item label="特殊任务计数默认值" prop="taskReportCount">
            <el-input-number v-model="formData.taskReportCount" :min="0" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="日期标题配置" prop="taskNameAppend">
        <el-radio-group v-model="formData.taskNameAppend">
          <el-radio :label="0">不追加</el-radio>
          <el-radio :label="1">追加</el-radio>
        </el-radio-group>

        <el-radio-group v-if="formData.taskNameAppend === 1" v-model="formData.taskAppendType"
          style="margin-top: 10px;">
          <el-radio :label="1">年</el-radio>
          <el-radio :label="2">季</el-radio>
          <el-radio :label="3">月</el-radio>
          <el-radio :label="4">周</el-radio>
          <el-radio :label="5">日</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="周期前移" prop="dateDurType" v-if="[2, 3, 4].includes(formData.taskAppendType)">
        <el-radio-group v-model="formData.dateDurType">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="生成类型" prop="taskCreateType">
        <el-radio-group v-model="formData.taskCreateType">
          <el-radio :label="0">默认修改</el-radio>
          <el-radio :label="1">新增</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="批量完成" prop="requiredItem">
        <!-- 这里保持你原有的TaskUnitExtraFormItem组件 -->
        <TaskUnitExtraFormItem type="radio" :bindKey="'batchCompletion'" :options="[
          { label: '允许', value: '0' },
          { label: '禁止', value: '1' }
        ]" v-model="formData.requiredItem" :disabled="isWorkbenchTemp" />
      </el-form-item>

      <el-form-item label="同组织可见" prop="accessLevel">
        <el-radio-group v-model="formData.accessLevel">
          <el-radio :label="0">不可见</el-radio>
          <el-radio :label="1">可见</el-radio>
        </el-radio-group>
      </el-form-item>
    </template>


    <el-row v-if="canRemind">
      <el-col :span="12">
        <el-form-item label="重点标记" prop="remind">
          <el-checkbox v-model="formData.remind" :disabled="!!formData.tipDate" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="canRemind">
      <el-col :span="12">
        <el-form-item label="提醒截止日期" prop="tipDate">
          <el-date-picker v-model="formData.tipDate" type="date" placeholder="请选择" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" @change="tipDateChange"
            :picker-options="{ disabledDate: (current) => new Date(current) < new Date() }" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="仅当日" prop="tipType">
          <el-radio-group v-model="formData.tipType">
            <el-radio label="2">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="提醒信息" prop="tipMessage" v-if="canRemind">
      <el-input type="textarea" v-model="formData.tipMessage" placeholder="请输入" clearable />
    </el-form-item>

    <el-form-item label="任务标签" prop="taskTags">
      <TaskUnitTagEdit v-model="formData.taskTags" />
    </el-form-item>
  </el-form>
</template>

<script>
import { getOrgTree } from '@/api/task-unit'
import { getTradeTypeList } from '@/api/trade-type'
import TaskUnitTagEdit from './TaskUnitTagEdit.vue'
import TaskUnitExtraFormItem from './TaskUnitExtraFormItem.vue'
import dayjs from 'dayjs'
import ElTreeSelect from '../../../../components/ElTreeSelect/index.vue'

export default {
  name: 'TaskUnitEditForm',

  components: {
    TaskUnitTagEdit,
    TaskUnitExtraFormItem,
    ElTreeSelect
  },

  props: {
    taskNameLabel: {
      type: String,
      default: '任务单元名称'
    },
    taskList: {
      type: Array
    },
    isTempTask: {
      type: Boolean,
      default: false
    },
    batchEdit: {
      type: Boolean,
      default: false
    },
    isWorkbenchTemp: {
      type: Boolean,
      default: false
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    isTemp: {
      type: Boolean,
      default: false
    },
    canRemind: {
      type: Boolean,
      default: false
    },
    // 接收 formData，模拟 v-model:formData
    formData: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      orgTree: [],
      tradeTypeList: [],
      formRef: null,
      user: this.$store ? this.$store.state.user : {}, // 如果用 Vuex，请根据项目调整
    }
  },

  computed: {
    isLeader() {
      const user = this.user
      return (
        user.username === 'admin' ||
        user.leader === 1 ||
        (user.role && user.role.split(',').includes('1'))
      )
    },

    formRules() {
      let rule = {
        taskName: [
          {
            required: true,
            message: '请输入任务单元名称'
          }
        ],
        taskTriggerId: [
          {
            required: true,
            message: '请选择触发器'
          }
        ],
        taskCompleteUnitId: [
          {
            required: true,
            message: '请选择监控单元'
          }
        ],
        taskAuditUnitId: [
          {
            required: true,
            message: '请选择监控单元'
          }
        ],
        checkOrgId: [
          {
            required: true,
            message: '请选择复核岗'
          }
        ],
        taskCheckId: [
          {
            required: true,
            message: '请选择复核人'
          },
          {
            validator: (rule, value, callback) => {
              if (
                this.formData.taskOwnerType === this.formData.taskCheckType &&
                this.formData.taskCheckType === '2' &&
                value === this.formData.taskOwnerId
              ) {
                callback(new Error('复核人不能与责任人相同'))
              } else {
                callback()
              }
            }
          }
        ],
        taskDeferredCount: [
          {
            required: true,
            message: '请输入顺延工作日数'
          }
        ]
      }
      if (!this.batchEdit) {
        rule = {
          ...rule,
          ownerOrgId: [
            {
              required: true,
              message: '请选择归属组织'
            }
          ],
          taskOwnerId: [
            {
              required: true,
              message: '请选择责任人'
            }
          ],
          taskStartTime: [
            {
              required: true,
              message: '请选择开始时间'
            },
            {
              validator: (rule, value, cb) => {
                const {
                  taskStartTime,
                  taskEndTime,
                  taskStartThreshold,
                  taskEndThreshold
                } = this.formData
                if (taskStartTime && taskEndTime) {
                  const startTime = new Date(taskStartTime).getTime()
                  const endTime = new Date(taskEndTime).getTime()
                  if (startTime >= endTime) {
                    cb(new Error('开始时间必须小于截止时间'))
                  } else if (taskStartThreshold > taskEndThreshold) {
                    cb(new Error('开始时间T值必须小于等于截止时间T值'))
                  } else {
                    cb()
                  }
                } else {
                  cb()
                }
              }
            }
          ],
          taskEndTime: [
            {
              required: true,
              message: '请选择截止时间'
            }
          ]
        }
      }
      return rule
    },

    dependTaskTree() {
      const map = new Map()
      const treeData = []
      if (!this.taskList) {
        return treeData
      }
      this.taskList.forEach((item) => {
        if (item.parentId === '0') {
          let children = map.get(item.id)
          if (!children) {
            children = []
            map.set(item.id, children)
          }
          treeData.push({
            ...item,
            disabled: item.id === this.formData.id,
            children
          })
        } else {
          let children = map.get(item.parentId)
          if (!children) {
            children = []
            map.set(item.parentId, children)
          }
          let childrenSelf = map.get(item.id)
          if (!childrenSelf) {
            childrenSelf = []
            map.set(item.id, childrenSelf)
          }
          children.push({
            ...item,
            disabled: item.id === this.formData.id,
            children: childrenSelf
          })
        }
      })
      return treeData
    },

    flatOrgList() {
      return this.handleFlatOrgTree(this.orgTree)
    },

    ownerUserList() {
      if (!this.formData.ownerOrgId) return []
      const org = this.flatOrgList.find(item => item.id === this.formData.ownerOrgId)
      return org ? org.userlist || [] : []
    },

    checkUserList() {
      if (!this.formData.checkOrgId) return []
      const org = this.flatOrgList.find(item => item.id === this.formData.checkOrgId)
      return org ? org.userlist || [] : []
    },

    taskStartTime() {
      return this.formData.taskStartTime ? new Date(this.formData.taskStartTime) : ''
    },

    taskEndTime() {
      return this.formData.taskEndTime ? new Date(this.formData.taskEndTime) : ''
    }
  },

  mounted() {
    getOrgTree().then(res => {
      this.orgTree = res.data
    })
    getTradeTypeList().then(res => {
      this.tradeTypeList = res.data
    })

    if (this.batchEdit) return
    const today = dayjs().format('YYYY-MM-DD')
    if (!this.formData.taskStartTime) {
      this.$set(this.formData, 'taskStartTime', today + ' 08:00:00')
    }
    if (!this.formData.taskEndTime) {
      this.$set(this.formData, 'taskEndTime', today + ' 17:00:00')
    }
  },

  methods: {
    handleFlatOrgTree(data, res = []) {
      data.forEach(item => {
        res.push(item)
        if (item.children) {
          this.handleFlatOrgTree(item.children, res)
        }
      })
      return res
    },

    tipDateChange() {
      if (this.formData.tipDate) {
        this.formData.remind = true
      }
    },

    async validate() {
      if (!this.$refs.formRef) {
        console.warn('formRef 未绑定')
        return false
      }
      try {
        await this.$refs.formRef.validate()
      } catch (error) {
        return false
      }

      this.formData.taskOwnerVal = ''
      if (this.formData.taskOwnerId) {
        const list = this.formData.taskOwnerType === '1' ? this.flatOrgList : this.ownerUserList
        const found = list.find(({ id }) => id === this.formData.taskOwnerId)
        this.formData.taskOwnerVal = found ? found.orgName || '' : ''
      }

      this.formData.taskCheckVal = ''
      if (this.formData.taskCheckReq === '1' && this.formData.taskCheckId) {
        const list = this.formData.taskCheckType === '1' ? this.flatOrgList : this.checkUserList
        const found = list.find(({ id }) => id === this.formData.taskCheckId)
        this.formData.taskCheckVal = found ? found.orgName || '' : ''
      }
      return this.formData
    },

    afterTaskTypeChange(val) {
      if (val === 'period') {
        this.formData.taskDeferredType = '0'
        this.formData.taskDeferredCount = 0
      } else {
        this.formData.taskStartThreshold = 0
        this.formData.taskEndThreshold = 0
      }
    },

    afterTaskOwnerTypeChange(val) {
      if (val === '1') {
        this.formData.taskOwnerId = this.formData.ownerOrgId
      } else {
        this.formData.taskOwnerId = ''
      }
    },

    afterOwnerOrgIdChange(val) {
      if (this.formData.taskOwnerType === '1') {
        this.formData.taskOwnerId = val
      }
    },

    afterTaskCheckTypeChange(val) {
      if (val === '1') {
        this.formData.taskCheckId = this.formData.checkOrgId
      } else {
        this.formData.taskCheckId = ''
      }
    },

    afterCheckOrgIdChange(val) {
      if (this.formData.taskCheckType === '1') {
        this.formData.taskCheckId = val
      }
    },

    checkNotEmptyValue(val, field, defaultValue) {
      if (!val) {
        setTimeout(() => {
          this.$set(this.formData, field, defaultValue)
        }, 150)
      }
    },

    handleTime(val, field) {
      const date = val ? `${dayjs().format('YYYY-MM-DD')} ${val}:00` : ''
      this.formData[field] = date
    },

    taskCheckReqChange() {
      if (this.formData.taskCheckReq === '0') {
        this.formData.taskCheckType = '1'
        this.formData.taskCheckId = ''
        this.formData.checkOrgId = ''
        this.formData.taskCheckVal = ''
      }
    },

    taskTriggerTypeChange() {
      if (this.formData.taskTriggerType === 'dynamic') {
        this.formData.taskType = 'period'
        this.afterTaskTypeChange('period')
      }
    }
  }
}
</script>
