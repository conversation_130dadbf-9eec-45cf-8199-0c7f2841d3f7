<template>
  <el-dialog :title="formData.id ? '编辑任务单元' : '新建任务单元'" :visible.sync="visible" width="60%"
    :close-on-click-modal="false" @close="handleCancel" @before-close="handleSave">
    <TaskUnitEditForm ref="formRef" :formData.sync="formData" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="saving" @click="handleSave">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import TaskUnitEditForm from './TaskUnitEditForm.vue'
import { saveTaskUnit, getTaskUnit } from '@/api/task-unit'
import { cloneDeep } from 'lodash'

export default {
  name: 'TaskUnitEditModal',
  components: {
    TaskUnitEditForm
  },
  data() {
    return {
      visible: false,
      saving: false,
      formData: this.getDefaultFormData()
    }
  },
  methods: {
    getDefaultFormData() {
      return {
        id: '',
        parentId: '',
        taskName: '',
        taskType: 'daily',
        taskTriggerType: 'manual',
        taskTriggerId: '',
        taskCronVal: '1',
        taskDesc: '',
        taskCompleteType: 'manual',
        taskCompleteUnitId: '',
        taskAuditType: '0',
        taskAuditUnitId: '',
        taskWarnNotice: 'web',
        taskPriority: '2',
        taskLevel: '2',
        taskAttachmentsType: '0',
        taskOwnerType: '1',
        taskOwnerId: '',
        ownerOrgId: '',
        taskOwnerVal: '',
        taskCheckReq: '0',
        taskCheckType: '1',
        taskCheckId: '',
        checkOrgId: '',
        taskCheckVal: '',
        taskStartTime: '',
        taskEndTime: '',
        taskStartThreshold: 0,
        taskEndThreshold: 0,
        taskTags: '',
        taskAuthType: '1',
        taskAuthId: '',
        taskAuthVal: '',
        taskAuthScope: 'ALL',
        taskDeferredType: '0',
        taskDeferredCount: 0,
        dependOnIds: '',
        requiredItem: JSON.stringify({ batchCompletion: '0' }),
        accessLevel: 0,
        workAmountFlag: 0,
        workAmount: 0,
        taskNameAppend: 0,
        taskAppendType: 1,
        taskCreateType: 0,
        contentParse: 0,
        dateDurType: 0,
        tipType: 0,
        taskReportFlag: 0,
        taskReportCount: 0
      }
    },
    async openModal(record) {
      if (!record) {
        this.formData = this.getDefaultFormData()
      } else {
        const res = (await getTaskUnit({ id: record.id })).data
        this.formData = Object.assign({}, res)
      }
      this.visible = true
    },
    async handleSave() {
      const valid = await this.$refs.formRef.validate()
      if (!valid) return
      console.log(this.formData)

      this.saving = true
      try {
        await saveTaskUnit(this.formData)
        this.$message.success('保存成功')
        this.visible = false
        this.$emit('success')
      } catch (e) {
        console.error(e)
      } finally {
        this.saving = false
      }
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
