<template>
  <div class="tag-edit-wrap">
    <el-tag v-for="tag in tags" :key="tag.id" closable @close="handleRemove(tag.id)" class="tag-item">
      {{ tag.name }}
    </el-tag>

    <el-input v-if="showInput" v-model.trim="inputVal" class="input-new-tag" size="mini" ref="inputRef"
      @keyup.enter.native="handleAdd" @blur="handleAdd" />

    <el-button v-else class="button-new-tag" size="mini" @click="handleEdit" type="dashed">
      <i class="el-icon-plus"></i>
      添加
    </el-button>
  </div>
</template>

<script>
import { nanoid } from 'nanoid';

export default {
  name: 'TagEditor',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showInput: false,
      inputVal: ''
    }
  },
  computed: {
    tags: {
      get() {
        try {
          return this.value ? JSON.parse(this.value) : []
        } catch (e) {
          return []
        }
      },
      set(val) {
        this.$emit('input', JSON.stringify(val))
      }
    }
  },
  methods: {
    handleEdit() {
      this.showInput = true
      this.$nextTick(() => {
        this.$refs.inputRef && this.$refs.inputRef.focus()
      })
    },
    handleAdd() {
      console.log(this.inputVal)
      if (this.inputVal) {
        const cache = this.tags.slice()
        cache.push({
          id: this.buildSimpleUUID(),
          name: this.inputVal
        })
        this.tags = cache
        this.inputVal = ''
      }
      this.showInput = false
    },
    handleRemove(id) {
      this.tags = this.tags.filter(tag => tag.id !== id)
    },
    buildSimpleUUID(size = 16) {
      const id = nanoid(size - 1)
      return id.charAt(0) === '0' ? '1' + id.slice(1) : id
    }
  }
}
</script>

<style scoped>
.tag-edit-wrap {
  padding: 4px 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.input-new-tag {
  width: 90px;
}

.button-new-tag {
  border-style: dashed;
}
</style>
