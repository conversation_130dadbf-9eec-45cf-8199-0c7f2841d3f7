<template>
  <div>
    <el-radio-group v-if="type === 'radio'" v-model="currentValue">
      <el-radio v-for="option in options" :key="option.value" :label="option.value">
        {{ option.label }}
      </el-radio>
    </el-radio-group>
  </div>
</template>

<script>
export default {
  name: 'CustomRadioBinding',
  props: {
    value: {
      // 用于 v-model（Vue 2 的 model 数据）
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'radio'
    },
    bindKey: {
      type: String,
      required: true
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    currentValue: {
      get() {
        try {
          return this.value ? JSON.parse(this.value)[this.bindKey] : ''
        } catch (e) {
          return ''
        }
      },
      set(val) {
        this.$emit('input', JSON.stringify({ [this.bindKey]: val }))
      }
    }
  }
}
</script>
