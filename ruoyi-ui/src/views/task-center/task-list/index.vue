<template>
  <div class="custom-main">
    <div class="operabar">
      <el-form :model="query" inline label-width="80px" class="query-form">
        <el-form-item label="任务名称">
          <el-input v-model="query.taskName" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select v-model="query.taskStatus" placeholder="请选择" clearable>
            <el-option label="正常" value="1" />
            <el-option label="异常" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务类型">
          <el-select v-model="query.taskType" placeholder="请选择" clearable>
            <el-option label="日常任务" value="daily" />
            <el-option label="周期任务" value="period" />
            <!-- <el-option label="邮件任务" value="mail" />
            <el-option label="OA任务" value="oa" /> -->
            <el-option label="临时待办" value="temp" />
          </el-select>
        </el-form-item>
        <el-form-item label="生成日期">
          <el-date-picker v-model="query.createTime" type="date" placeholder="请选择" clearable format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" />
        </el-form-item>

        <el-form-item>
          <el-button @click="batchAudit" v-if="selectedKeys.length > 0">批量复核</el-button>
          <el-button @click="handleReset" icon="el-icon-refresh">重置</el-button>
          <el-button type="primary" @click="handleSearch" icon="el-icon-search">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-tabs v-model="activeTab" type="card" class="task-list-tabs">
      <el-tab-pane label="任务列表" name="1">
        <task-list-table ref="taskListTableRef" :query-params="query"></task-list-table>
      </el-tab-pane>
      <el-tab-pane label="待复核任务" name="2">
        <check-task-list-table ref="checkTaskListTableRef" :query-params="query"
          :selected-keys.sync="selectedKeys"></check-task-list-table>
      </el-tab-pane>
    </el-tabs>

    <audit-modal ref="auditModalRef" @success="handleSearch"></audit-modal>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import TaskListTable from './components/TaskListTable.vue'
import CheckTaskListTable from './components/CheckTaskListTable.vue'
import AuditModal from './components/AuditModal.vue'

export default {
  name: 'TaskManager',
  components: {
    TaskListTable,
    CheckTaskListTable,
    AuditModal,
  },
  data() {
    return {
      query: {
        taskName: '',
        taskStatus: undefined,
        taskType: undefined,
        createTime: dayjs().format('YYYY-MM-DD'),
      },
      selectedKeys: [],
      activeTab: '1',
    }
  },
  methods: {
    handleSearch() {
      if (this.$refs.taskListTableRef && this.$refs.taskListTableRef.fetchData) {
        this.$refs.taskListTableRef.fetchData()
      }
      if (this.$refs.checkTaskListTableRef && this.$refs.checkTaskListTableRef.sendGetTaskPage) {
        this.$refs.checkTaskListTableRef.sendGetTaskPage()
      }
    },
    handleReset() {
      this.query = {
        taskName: '',
        taskStatus: undefined,
        taskType: undefined,
        createTime: dayjs().format('YYYY-MM-DD'),
      }
      this.handleSearch()
    },
    batchAudit() {
      if (this.$refs.auditModalRef && this.$refs.auditModalRef.openBatchModal) {
        this.$refs.auditModalRef.openBatchModal(this.selectedKeys)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.task-list-tabs {
  background-color: #fff;

  .el-tabs__content {
    padding-top: 0;
  }
}

.operabar {
  margin-bottom: 10px;
}

.query-form {
  align-items: center;
}

.el-form-item {
  margin-right: 15px;
}

.custom-main {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px;
}
</style>
