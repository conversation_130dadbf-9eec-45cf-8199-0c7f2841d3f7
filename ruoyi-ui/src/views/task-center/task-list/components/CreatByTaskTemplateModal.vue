<template>
  <el-dialog title="新建任务" :visible.sync="visible" width="440px" :before-close="handleBeforeClose"
    custom-class="task-drawer-dialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item label="基于任务模板" prop="id">
        <el-select v-model="formData.id" placeholder="请选择" @change="handleChangeTaskTemplate" filterable clearable>
          <el-option v-for="item in taskTemplateList" :key="item.id" :label="item.templateName" :value="item.id" />
        </el-select>
      </el-form-item>

      <template v-if="formData.id">
        <el-divider></el-divider>

        <el-form-item label="生成周期" prop="schedulerType" :show-message="false">
          <el-select v-model="formData.schedulerType" placeholder="请选择">
            <el-option label="立即生成" value="manual"></el-option>
            <!-- 你可以根据需求放开下面选项 -->
            <!--
            <el-option label="工作日" value="daily"></el-option>
            <el-option label="自定义" value="dynamic"></el-option>
            -->
          </el-select>
        </el-form-item>

        <el-form-item v-if="formData.schedulerType === 'dynamic'" label="触发器" prop="triggerId">
          <el-select v-model="formData.triggerId" placeholder="请选择" filterable clearable>
            <el-option v-for="item in tradeTypeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="formData.schedulerType === 'dynamic'" label="触发频率" prop="cronVal">
          <el-select v-model="formData.cronVal" placeholder="请选择">
            <el-option label="1小时" value="1"></el-option>
            <el-option label="2小时" value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-divider></el-divider>

        <div v-if="formData.list.length" class="tree-wrapper">
          <el-tree :data="tempTaskTree" node-key="id" :props="treeProps" default-expand-all highlight-current>
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ data.taskName }}</span>
              <el-dropdown trigger="hover" @command="handleCommand(data)">
                <span class="el-dropdown-link">
                  <i class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </el-tree>
        </div>
      </template>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </span>

    <temp-task-unit-edit ref="tempTaskUnitEditRef" @success="doEditTempTask" />
  </el-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import { createTaskByTemplate } from '@/api/task'
import { getTaskTemplate, getTaskTemplateList } from '@/api/task-template'
import { getTradeTypeList } from '@/api/trade-type'
import TempTaskUnitEdit from '../../task-template/components/TempTaskUnitEdit.vue'

export default {
  components: {
    TempTaskUnitEdit
  },
  data() {
    return {
      visible: false,
      taskTemplateList: [],
      tradeTypeList: [],
      formData: {
        id: '',
        templateName: '',
        templateType: '1',
        templateUse: '',
        templateStatus: 0,
        schedulerType: 'manual',
        triggerId: '',
        cronVal: '1',
        orgId: '',
        remark: '',
        list: []
      },
      formRules: {
        id: [
          { required: true, message: '请选择基于任务模板', trigger: 'change' }
        ],
        triggerId: [
          { required: true, message: '请选择触发器', trigger: 'change' }
        ]
      },
      treeProps: {
        children: 'children',
        label: 'taskName'
      }
    }
  },
  computed: {
    tempTaskTree() {
      const map = new Map()
      const treeData = []

      this.formData.list.forEach(item => {
        if (item.parentId === '0') {
          let children = map.get(item.id)
          if (!children) {
            children = []
            map.set(item.id, children)
          }
          treeData.push({
            ...item,
            children
          })
        } else {
          let children = map.get(item.parentId)
          if (!children) {
            children = []
            map.set(item.parentId, children)
          }
          let childrenSelf = map.get(item.id)
          if (!childrenSelf) {
            childrenSelf = []
            map.set(item.id, childrenSelf)
          }
          children.push({
            ...item,
            children: childrenSelf
          })
        }
      })
      return treeData
    }
  },
  methods: {
    openModal() {
      getTaskTemplateList({ self: true }).then(res => {
        this.taskTemplateList = res.data
      })
      getTradeTypeList().then(res => {
        this.tradeTypeList = res.data
      })

      this.formData = cloneDeep({
        id: '',
        templateName: '',
        templateType: '1',
        templateUse: '',
        templateStatus: 0,
        schedulerType: 'manual',
        triggerId: '',
        cronVal: '1',
        orgId: '',
        remark: '',
        list: []
      })
      this.visible = true
    },
    handleChangeTaskTemplate(id) {
      getTaskTemplate({ id }).then(taskTemplate => {
        taskTemplate.data.list.forEach(row => {
          row.taskTriggerType = 'manual'
        })
        this.formData = cloneDeep(taskTemplate.data)
        this.formData.schedulerType = 'manual'
      })
    },
    handleCommand(data) {
      this.handleEditTempTask(data)
    },
    handleEditTempTask(record) {
      this.$refs.tempTaskUnitEditRef.openModal(record)
    },
    doEditTempTask(record) {
      const index = this.formData.list.findIndex(item => item.id === record.id)
      if (index !== -1) {
        this.$set(this.formData.list, index, record)
      }
    },
    handleSave() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        createTaskByTemplate(this.formData).then(() => {
          this.$message.success('保存成功')
          this.visible = false
          this.$emit('success')
        }).catch(() => {
          this.$message.error('保存失败')
        })
      })
    },
    handleBeforeClose(done) {
      done()
    }
  }
}
</script>

<style scoped>
.task-drawer-dialog {
  top: 0 !important;
  margin: 0 !important;
  height: 100% !important;
  max-width: 440px !important;
  width: 440px !important;
  padding-bottom: 40px;
}

.tree-wrapper {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 8px;
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
