<template>
  <div>
    <!-- 多选批量操作按钮 -->
    <div v-if="selectedKeys.length > 0 && inWorkbench && (!activeKey || activeKey === 2)" class="table-multi-action"
      :style="{ bottom: `${multiActionBottom}px` }">
      <el-button type="primary" @click="multiCheck">批量复核</el-button>
    </div>

    <!-- 表格 -->
    <el-table :data="data" :loading="loading" style="width: 100%" border @selection-change="handleSelectionChange"
      :row-key="row => row.id">
      <el-table-column type="selection" width="55">
      </el-table-column>

      <el-table-column prop="levelNo" label="层级编号" width="100"></el-table-column>

      <el-table-column label="任务名称" min-width="180" scopedSlots="taskName">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.taskName" placement="top">
            <div class="task-name-cell">{{ scope.row.taskName }}</div>
          </el-tooltip>
          <el-tag v-if="scope.row.taskTransferStatus === '1'" type="purple" size="small">转</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" prop="taskCompleteStatus" width="80" :formatter="formatStatus"></el-table-column>

      <el-table-column label="生成日期" prop="taskGenTime" width="100"></el-table-column>

      <el-table-column label="截止日期" prop="taskEndTime" width="100"></el-table-column>

      <el-table-column label="责任归属" prop="taskOwnerVal" width="100"></el-table-column>

      <el-table-column label="复核归属" prop="taskCheckVal" width="100"></el-table-column>

      <el-table-column label="操作" width="150" scopedSlots="opera">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleCheck(scope.row)">复核</el-button>
          <el-button type="text" size="small" @click="handleShowDesc(scope.row)">说明</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination background layout="prev, pager, next, sizes, total" :total="total"
      :page-size="queryPageData.pageSize" :current-page="queryPageData.page" @current-change="handlePageChange"
      @size-change="handlePageSizeChange" style="margin-top: 10px; text-align: right;">
    </el-pagination>

    <!-- 弹窗组件 -->
    <CreatByTaskUnitModal ref="creatByTaskUnitModalRef" @success="sendGetTaskPage" />
    <AuditModal ref="auditModalRef" @success="actionConfirm" />
  </div>
</template>

<script>
import { cloneDeep, debounce } from 'lodash'
import { getAuditTaskPage } from '@/api/task'
import CreatByTaskUnitModal from './CreatByTaskUnitModal.vue'
import AuditModal from './AuditModal.vue'
import pubSub from '@/utils/publish'

export default {
  name: 'TaskTable',
  components: { CreatByTaskUnitModal, AuditModal },
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    },
    inWorkbench: {
      type: Boolean,
      default: false
    },
    activeKey: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      selectedKeys: [],
      multiActionBottom: 183,
      scrollDiff: 0,
      columns: [
        // 用于列选择，示意。Element UI 无内建列过滤功能，可自定义
        '层级编号',
        '任务名称',
        '状态',
        '生成日期',
        '截止日期',
        '责任归属',
        '复核归属',
        '操作'
      ],
      showColumns: [],
      loading: false,
      data: [],
      queryPageData: {
        page: 1,
        pageSize: 20
      },
      total: 0
    }
  },
  computed: {
    query() {
      return Object.assign({}, this.queryPageData, this.queryParams)
    }
  },
  watch: {
    showColumns(val) {
      localStorage.setItem('workbenchCheckShowColumns', JSON.stringify(val))
    }
  },
  mounted() {
    const storageColumns = localStorage.getItem('workbenchCheckShowColumns')
    if (storageColumns) {
      this.showColumns = JSON.parse(storageColumns)
    } else {
      this.showColumns = cloneDeep(this.columns)
    }

    this.$nextTick(() => {
      const scrollElement = document.getElementById('workbench-scroll')
      setTimeout(() => {
        if (!scrollElement) return
        this.scrollDiff = scrollElement.scrollHeight - scrollElement.clientHeight
        this.multiActionBottom = this.scrollDiff - 20

        const handleScroll = () => {
          const scrollTop = scrollElement.scrollTop
          this.multiActionBottom = this.scrollDiff - scrollTop - (scrollTop < this.scrollDiff ? 20 : 0)

        }
        this.debouncedHandleScroll = debounce(handleScroll, 10)
        scrollElement.addEventListener('scroll', this.debouncedHandleScroll)
      }, 50)
    })

    this.sendGetTaskPage()
  },
  beforeDestroy() {
    const scrollElement = document.getElementById('workbench-scroll')
    if (scrollElement && this.debouncedHandleScroll) {
      scrollElement.removeEventListener('scroll', this.debouncedHandleScroll)
    }
  },
  methods: {
    formatStatus(row, column, cellValue) {
      const cfg = {
        '0': { label: '未开始', type: '' },
        '1': { label: '进行中', type: 'warning' },
        '2': { label: '待复核', type: 'info' },
        '3': { label: '已完成', type: 'success' },
        '5': { label: '未发生', type: '' }
      }
      const status = cfg[cellValue] || { label: '', type: '' }
      return `<el-tag type="${status.type}">${status.label}</el-tag>`
    },
    handleSelectionChange(selection) {
      this.selectedKeys = selection.map(item => item.id)
    },
    travelTableData(tableData, levelNo) {
      tableData.forEach((item, index) => {
        const myLevelNo = `${levelNo ? levelNo + '-' : ''}${index + 1}`
        this.$set(item, 'levelNo', myLevelNo)
        if (item.taskCompleteStatus === '1' && (new Date().getTime() < new Date(item.taskStartTime).getTime())) {
          this.$set(item, 'taskCompleteStatus', '0')
        }
        if (item.children) {
          this.travelTableData(item.children, myLevelNo)
        }
      })
    },
    async sendGetTaskPage() {
      this.loading = true
      try {
        const params = this.query
        if (this.inWorkbench) params.pageSize = 999
        const res = (await getAuditTaskPage(params)).data
        this.data = res
        this.selectedKeys = []
        this.total = res.length // 如果后端支持分页，这里应改为实际总数
        this.travelTableData(this.data)
      } finally {
        this.loading = false
      }
    },
    multiCheck() {
      this.$refs.auditModalRef.openBatchModal(this.selectedKeys)
    },
    handlePageChange(page) {
      this.queryPageData.page = page
      this.sendGetTaskPage()
    },
    handlePageSizeChange(size) {
      this.queryPageData.pageSize = size
      this.sendGetTaskPage()
    },
    handleCheck(record) {
      this.$refs.auditModalRef.openModal(record)
    },
    handleShowDesc(record) {
      this.$alert(record.taskDesc, '说明', {
        confirmButtonText: '关闭',
        dangerouslyUseHTMLString: false,
        type: 'info'
      })
    },
    actionConfirm() {
      if (this.inWorkbench) {
        pubSub.publish('loadStatistics')
        pubSub.publish('loadDailyList')
      }
      this.sendGetTaskPage()
    }
  }
}
</script>

<style scoped lang="scss">
.task-name-cell {
  max-width: calc(100% - 34px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}

.table-multi-action {
  position: fixed;
  right: 20px;
  z-index: 1000;
}

.columns-filter {
  padding: 10px;
  background: #fff;
  border: 1px solid #ebebeb;
}

.columns-filter-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
</style>
