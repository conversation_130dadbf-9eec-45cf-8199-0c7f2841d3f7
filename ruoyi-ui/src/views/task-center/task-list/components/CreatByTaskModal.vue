<template>
  <el-drawer title="新建任务" :visible.sync="visible" :size="720" :with-header="true" :before-close="handleBeforeClose"
    :modal-append-to-body="true">
    <!-- 任务编辑表单组件 -->
    <TaskUnitEditForm ref="formRef" :form-data.sync="formData" task-name-label="任务名称" :is-temp-task="true" />
    <div class="drawer-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { cloneDeep } from 'lodash'
import { createTaskByTemp } from '@/api/task'
import TaskUnitEditForm from '../../task-unit/components/TaskUnitEditForm.vue'
import { Message } from 'element-ui'

export default {
  name: 'CreateTaskDrawer',
  components: {
    TaskUnitEditForm
  },
  data() {
    return {
      visible: false,
      defaultTask: {
        id: '',
        parentId: '',
        taskName: '',
        taskType: 'temp',
        taskTriggerType: 'manual',
        taskTriggerId: '',
        taskCronVal: '1',
        taskDesc: '',
        taskCompleteType: 'manual',
        taskCompleteUnitId: '',
        taskAuditType: '0',
        taskAuditUnitId: '',
        taskWarnNotice: 'web',
        taskPriority: '2',
        taskLevel: '2',
        taskAttachmentsType: '0',
        taskOwnerType: '1',
        taskOwnerId: '',
        ownerOrgId: '',
        taskOwnerVal: '',
        taskCheckReq: '0',
        taskCheckType: '1',
        taskCheckId: '',
        checkOrgId: '',
        taskCheckVal: '',
        taskStartTime: '',
        taskEndTime: '',
        taskStartThreshold: 0,
        taskEndThreshold: 0,
        taskTags: '',
        taskAuthType: '1',
        taskAuthId: '',
        taskAuthVal: '',
        taskAuthScope: 'ALL',
        taskDeferredType: '0',
        taskDeferredCount: 0,
        dependOnIds: '',
        requiredItem: JSON.stringify({ batchCompletion: '0' }),
        accessLevel: 0,
        taskStatus: '1',
        taskRef: '2',
        taskRefId: '',
        taskRefIdType: '1',
        taskGenTime: '',
        taskProgress: '',
        taskProcessStatus: '1',
        taskCompleteStatus: '0',
        taskCompleteDesc: '',
        completeTime: '',
        taskTransferStatus: '0',
        taskTransferUserId: '',
        taskTransferDesc: '',
        taskCheckStatus: '0',
        taskCheckDesc: '',
        workAmountFlag: 0,
        workAmount: 0,
        taskNameAppend: 0,
        taskAppendType: 1,
        taskCreateType: 0,
        contentParse: 0,
        dateDurType: 0,
        taskReportFlag: 0,
        taskReportCount: 0,
        reminder: 0,
        tipType: 0
      },
      formData: {}
    }
  },
  methods: {
    openModal() {
      this.formData = cloneDeep(this.defaultTask)
      this.visible = true
    },
    handleSave() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return false
        try {
          await createTaskByTemp(this.formData)
          Message.success('保存成功')
          this.visible = false
          this.$emit('success')
        } catch (error) {
          Message.error('保存失败')
        }
      })
    },
    handleBeforeClose(done) {
      // 如果需要，可以加关闭前逻辑
      done()
    }
  }
}
</script>

<style scoped>
.drawer-footer {
  text-align: right;
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
}
</style>
