<template>
  <el-dialog title="说明" :visible.sync="visible" :close-on-click-modal="false" :modal-append-to-body="false" width="40%"
    :draggable="true">
    <div class="description-line">
      <div class="description-line-title">
        <div class="description-line-title-logo">
          <img src="@/assets/icons/svg/description.svg" alt="" />
        </div>
        <div class="description-line-title-text">任务说明</div>
        <div class="description-line-title-edit" @click="changeEdit">
          <template v-if="!isEdit">
            <i class="el-icon-edit"></i>
          </template>
          <template v-else-if="loading">
            <i class="el-icon-loading"></i>
          </template>
          <template v-else>
            <i class="el-icon-check"></i>
          </template>
        </div>
      </div>

      <div class="description-line-content" v-if="!isEdit">{{ currentRecord.taskDesc }}</div>
      <el-input type="textarea" v-else v-model="taskDescEdit" :rows="4" style="margin-top: 10px;"></el-input>
    </div>

    <div class="description-block"
      :style="{ gridTemplateColumns: currentRecord.taskCheckReq === '0' ? 'repeat(1, 1fr)' : 'repeat(2, 1fr)' }">
      <div class="description-line">
        <div class="description-line-title">
          <div class="description-line-title-logo">
            <img src="@/assets/icons/svg/description.svg" alt="" />
          </div>
          <div class="description-line-title-text">完成备注</div>
        </div>
        <div class="description-line-content">{{ currentRecord.taskCompleteDesc }}</div>
      </div>

      <div class="description-line" v-if="currentRecord.taskCheckReq === '1'">
        <div class="description-line-title">
          <div class="description-line-title-logo">
            <img src="@/assets/icons/svg/description.svg" alt="" />
          </div>
          <div class="description-line-title-text">复核备注</div>
        </div>
        <div class="description-line-content">{{ currentRecord.taskCheckDesc }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { rewriteTaskDesc } from '@/api/task'
import { Message } from 'element-ui'

export default {
  data() {
    return {
      visible: false,
      currentRecord: {},
      isEdit: false,
      taskDescEdit: '',
      loading: false,
    }
  },
  methods: {
    openModal(record) {
      this.currentRecord = record || {}
      this.taskDescEdit = record && record.taskDesc ? record.taskDesc : ''
      this.visible = true
      this.isEdit = false
      this.loading = false
    },
    async changeEdit() {
      if (this.isEdit) {
        this.loading = true
        try {
          await rewriteTaskDesc({
            id: this.currentRecord.id,
            taskRefId: this.currentRecord.taskRefId,
            taskDesc: this.taskDescEdit,
            taskBindTemplateId: this.currentRecord.taskBindTemplateId,
          })
          this.currentRecord.taskDesc = this.taskDescEdit
          Message.success('保存成功')
        } catch (error) {
          Message.error('保存失败')
        } finally {
          this.loading = false
        }
      }
      this.isEdit = !this.isEdit
    },
  },
}
</script>

<style scoped lang="scss">
.description-block {
  display: grid;
  gap: 16px;
  margin-top: 20px;
}

.description-line {
  padding: 16px;
  background: rgb(247, 248, 250);
  border-radius: 4px;

  .description-line-title {
    display: flex;
    align-items: center;

    .description-line-title-text {
      margin-left: 5px;
      font-size: 16px;
    }

    .description-line-title-edit {
      margin-left: 5px;
      font-size: 16px;
      cursor: pointer;
    }
  }

  .description-line-content {
    margin-top: 10px;
    color: #888;
  }
}
</style>
