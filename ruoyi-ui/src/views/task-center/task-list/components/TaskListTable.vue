<template>
  <div>
    <el-table :data="dataList" v-loading="loading" stripe border :row-key="rowKey" style="width: 100%">
      <el-table-column v-for="col in getColumns" :key="col.title" :prop="col.dataIndex" :label="col.title"
        :width="col.width">
        <template slot-scope="scope">
          <template v-if="col.slotName === 'taskName'">
            <el-tooltip class="item" effect="dark" :content="scope.row.taskName" placement="top">
              <span>{{ scope.row.taskName }}</span>
            </el-tooltip>
            <el-tag v-if="scope.row.taskTransferStatus === '1'" type="info" size="mini">转</el-tag>
          </template>
          <template v-else-if="col.dataIndex === 'taskCompleteStatus'">
            <el-tag :type="statusCfg(scope.row.taskCompleteStatus).type" size="mini">
              {{ statusCfg(scope.row.taskCompleteStatus).label }}
            </el-tag>
          </template>
          <template v-else-if="col.dataIndex === 'taskGenTime'">
            {{ formatDate(scope.row.taskGenTime) }}
          </template>
          <template v-else-if="col.dataIndex === 'taskType'">
            {{ typeLabel(scope.row.taskType) }}
          </template>
          <template v-else-if="col.slotName === 'opera'">
            <el-button type="text" size="mini" @click="handleComplete(scope.row)">完成</el-button>
            <el-button type="text" size="mini" @click="handleShowDesc(scope.row)">说明</el-button>
            <el-dropdown trigger="hover" placement="bottom-end">
              <span class="el-dropdown-link">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="handleUnrealized(scope.row)">
                  未发生
                </el-dropdown-item>
                <el-dropdown-item @click.native="handleTransfer(scope.row)">
                  转派
                </el-dropdown-item>
                <el-dropdown-item @click.native="handleUpload(scope.row.id)">
                  上传附件
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-else>
            {{ scope.row[col.dataIndex] }}
          </template>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination style="margin-top: 15px; text-align: right;" background layout="prev, pager, next, sizes, total"
      :current-page.sync="queryPageData.page" :page-size.sync="queryPageData.pageSize" :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total" @current-change="handlePageChange" @size-change="handlePageSizeChange">
    </el-pagination>

    <!-- Modals -->
    <creat-by-task-unit-modal ref="unitModal" @success="fetchData" />
    <creat-by-task-template-modal ref="templateModal" @success="fetchData" />
    <creat-by-task-modal ref="manualModal" @success="fetchData" />
    <transfer-modal ref="transferModal" @success="fetchData" />
    <file-list-modal ref="fileModal" />
    <complete-modal ref="completeModal" @success="fetchData" />
    <description-modal ref="descModal" />
    <unrealized-modal ref="unrealizedModal" @success="fetchData" />
  </div>
</template>

<script>
import { map } from 'lodash'
import dayjs from 'dayjs'
import { getTaskPage } from '@/api/task'
import CreatByTaskUnitModal from './CreatByTaskUnitModal.vue'
import CreatByTaskTemplateModal from './CreatByTaskTemplateModal.vue'
import CreatByTaskModal from './CreatByTaskModal.vue'
import TransferModal from './TransferModal.vue'
import FileListModal from './FileListModal.vue'
import CompleteModal from './WorkbenchCompleteModal.vue'
import DescriptionModal from './DescriptionModal.vue'
import UnrealizedModal from './UnrealizedModal.vue'

export default {
  name: 'TaskTable',
  components: {
    CreatByTaskUnitModal,
    CreatByTaskTemplateModal,
    CreatByTaskModal,
    TransferModal,
    FileListModal,
    CompleteModal,
    DescriptionModal,
    UnrealizedModal
  },
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const cols = [
      { title: '层级编号', dataIndex: 'levelNo' },
      { title: '任务名称', slotName: 'taskName' },
      { title: '状态', dataIndex: 'taskCompleteStatus' },
      { title: '开始时间', dataIndex: 'taskStartTime' },
      { title: '截止时间', dataIndex: 'taskEndTime' },
      { title: '责任归属', dataIndex: 'taskOwnerVal' },
      { title: '操作', slotName: 'opera', width: 120 }
    ]
    return {
      columns: cols,
      showColumns: JSON.parse(localStorage.getItem('showColumns') || JSON.stringify(map(cols, 'title'))),
      dataList: [],
      loading: false,
      queryPageData: { page: 1, pageSize: 20 },
      pagination: { total: 0 }
    }
  },
  computed: {
    getColumns() {
      return this.columns.filter(col => this.showColumns.includes(col.title))
    },
    rowKey() {
      return 'id'
    }
  },
  methods: {
    toggleColumn() {
      localStorage.setItem('showColumns', JSON.stringify(this.showColumns))
    },
    formatDate(val) {
      return dayjs(val).format('YYYY-MM-DD')
    },
    statusCfg(status) {
      const map = {
        '0': { label: '未开始', type: 'info' },
        '1': { label: '进行中', type: 'warning' },
        '2': { label: '待复核', type: 'primary' },
        '3': { label: '已完成', type: 'success' },
        '5': { label: '未发生', type: 'info' }
      }
      return map[status] || { label: '', type: '' }
    },
    typeLabel(type) {
      const map = { daily: '日常任务', period: '周期任务', temp: '临时任务', oa: 'OA任务', mail: '邮件任务' }
      return map[type] || ''
    },
    travelTableData(data, parentLevel = '') {
      data.forEach((item, idx) => {
        const levelNo = parentLevel ? `${parentLevel}-${idx + 1}` : `${idx + 1}`
        this.$set(item, 'levelNo', levelNo)
        if (item.taskCompleteStatus === '1' && new Date() < new Date(item.taskStartTime)) {
          item.taskCompleteStatus = '0'
        }
        if (item.children && item.children.length) {
          this.travelTableData(item.children, levelNo)
        }
      })
    },
    async fetchData() {
      this.loading = true
      const res = (await getTaskPage({
        page: this.queryPageData.page,
        pageSize: this.queryPageData.pageSize,
        ...this.queryParams
      })).data
      this.loading = false
      this.pagination.total = res.total || 0
      const records = res.records || []
      this.travelTableData(records)
      this.dataList = records
      console.log(this.dataList)
    },
    handlePageChange(page) {
      this.queryPageData.page = page
      this.fetchData()
    },
    handlePageSizeChange(size) {
      this.queryPageData.pageSize = size
      this.fetchData()
    },
    handleComplete(row) {
      this.$refs.completeModal.openModal(row, this.getRootId(row.id))
    },
    handleShowDesc(row) {
      this.$refs.descModal.openModal(row)
    },
    handleTransfer(row) {
      this.$refs.transferModal.openModal(row)
    },
    handleUpload(id) {
      this.$refs.fileModal.openModal(id)
    },
    handleUnrealized(row) {
      this.$refs.unrealizedModal.openModal(row)
    },
    getRootId(id) {
      const flat = []
      const dfs = arr => {
        arr.forEach(item => {
          flat.push({ ...item, children: [] })
          if (item.children) dfs(item.children)
        })
      }
      dfs(this.dataList)
      const found = flat.find(i => i.parentId === '0' && i.taskChildIds?.split(',').includes(id))
      return found ? found.id : ''
    }
  },
  mounted() {
    this.fetchData()
  }
}
</script>

<style scoped>
.task-name-cell {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
