<template>
  <el-drawer title="上传附件" :visible.sync="visible" size="460px" :destroy-on-close="true">
    <div style="margin-bottom: 16px;">
      <el-upload :action="uploadAction" :auto-upload="false" :show-file-list="false" :before-upload="beforeUpload"
        :on-change="handleUpload" :http-request="customHttpRequest">
        <el-button size="small" type="primary">选择文件</el-button>
      </el-upload>
    </div>

    <el-table :data="taskFileList" border style="width: 100%">
      <el-table-column prop="name" label="文件名称" />
      <el-table-column prop="dateTime" label="上传时间" />
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleDownload(scope.row.id)">下载</el-button>
          <el-dropdown trigger="hover" placement="bottom-end">
            <span class="el-dropdown-link icon-btn-simple">
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item divided style="color: #f56c6c" @click.native="handleDelete(scope.row.id)">
                <i class="el-icon-delete" style="margin-right: 4px;"></i>删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </el-drawer>
</template>

<script>
import {
  getTaskFileList,
  downloadFile,
  deleteFile,
  uploadFile,
} from '@/api/task'
import { Message } from 'element-ui'

export default {
  data() {
    return {
      visible: false,
      taskId: '',
      taskFileList: [],
      uploadAction: '/', // 这里不实际使用，上传用自定义请求
    }
  },
  methods: {
    async openModal(id) {
      this.taskId = id
      await this.sendGetTaskFileList()
      this.visible = true
    },
    async sendGetTaskFileList() {
      try {
        const data = (await getTaskFileList({ taskId: this.taskId })).data
        this.taskFileList = data
      } catch (error) {
        Message.error('获取附件列表失败')
      }
    },
    beforeUpload(file) {
      // 可以添加文件名长度等校验
      // if (file.name.split('.')[0].length > 50) {
      //   Message.warning('上传文件名不能超过50个字符')
      //   return false
      // }
      return true
    },
    async handleUpload(file) {
      // file参数是ElUpload组件包装后的对象，真实文件在file.raw
      const realFile = file.raw || file
      const formData = new FormData()
      formData.append('taskId', this.taskId)
      formData.append('file', realFile)
      try {
        await uploadFile(formData)
        Message.success('上传成功')
        this.sendGetTaskFileList()
      } catch (error) {
        Message.error('上传失败')
      }
    },
    handleDownload(id) {
      downloadFile({ id })
    },
    async handleDelete(id) {
      try {
        await deleteFile({ id })
        Message.success('删除成功')
        this.sendGetTaskFileList()
      } catch (error) {
        Message.error('删除失败')
      }
    },
    // 自定义上传请求，阻止默认上传，使用自己接口
    async customHttpRequest({ file }) {
      // 这里写上传逻辑（和handleUpload类似）
      await this.handleUpload(file)
    },
  },
}
</script>

<style scoped>
.icon-btn-simple {
  cursor: pointer;
  font-size: 18px;
}
</style>
