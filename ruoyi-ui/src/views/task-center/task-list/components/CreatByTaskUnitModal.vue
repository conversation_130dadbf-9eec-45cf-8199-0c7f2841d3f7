<template>
  <el-dialog title="新建任务" :visible.sync="visible" width="720px" :before-close="handleBeforeClose"
    custom-class="task-drawer-dialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="基于任务单元" prop="taskRefId">
            <el-select v-model="formData.taskRefId" placeholder="请选择" filterable clearable
              @change="handleChangeTaskUnit">
              <el-option v-for="item in taskUnitList" :key="item.id" :label="item.taskName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div v-if="formData.taskRefId" style="margin-top: 20px;">
      <el-divider></el-divider>
      <!-- 这里是子表单组件 -->
      <task-unit-edit-form ref="formRef2" :form-data.sync="formData" :task-name-label="'任务名称'" :is-create="true" />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import { Message } from 'element-ui'
import { getTaskUnit, getTaskUnitList, createTaskByUnit } from '@/api/task-unit' // 请确认路径
import TaskUnitEditForm from '../../task-unit/components/TaskUnitEditForm.vue'

export default {
  components: {
    TaskUnitEditForm
  },
  data() {
    return {
      visible: false,
      taskUnitList: [],
      formData: {
        id: '',
        parentId: '',
        taskName: '',
        taskType: 'daily',
        taskTriggerType: 'manual',
        taskTriggerId: '',
        taskCronVal: '1',
        taskDesc: '',
        taskCompleteType: 'manual',
        taskCompleteUnitId: '',
        taskAuditType: '0',
        taskAuditUnitId: '',
        taskWarnNotice: 'web',
        taskPriority: '2',
        taskLevel: '2',
        taskAttachmentsType: '0',
        taskOwnerType: '1',
        taskOwnerId: '',
        ownerOrgId: '',
        taskOwnerVal: '',
        taskCheckReq: '0',
        taskCheckType: '1',
        taskCheckId: '',
        checkOrgId: '',
        taskCheckVal: '',
        taskStartTime: '',
        taskEndTime: '',
        taskStartThreshold: 0,
        taskEndThreshold: 0,
        taskTags: '',
        taskAuthType: '1',
        taskAuthId: '',
        taskAuthVal: '',
        taskAuthScope: 'ALL',
        taskDeferredType: '0',
        taskDeferredCount: 0,
        dependOnIds: '',
        requiredItem: '',
        taskStatus: '1',
        taskRef: '2',
        taskRefId: '',
        taskRefIdType: '1',
        taskGenTime: '',
        taskProgress: '',
        taskProcessStatus: '1',
        taskCompleteStatus: '0',
        taskCompleteDesc: '',
        completeTime: '',
        taskTransferStatus: '0',
        taskTransferUserId: '',
        taskTransferDesc: '',
        taskCheckStatus: '0',
        taskCheckDesc: '',
        accessLevel: 0,
        workAmountFlag: 0,
        workAmount: 0,
        taskNameAppend: 0,
        taskAppendType: 1,
        taskCreateType: 0,
        contentParse: 0,
        dateDurType: 0,
        taskReportFlag: 0,
        taskReportCount: 0,
        reminder: 0,
        tipType: 0
      },
      formRules: {
        taskRefId: [
          { required: true, message: '请选择基于任务单元', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    openModal() {
      getTaskUnitList().then(res => {
        this.taskUnitList = res.data
      })
      this.formData = cloneDeep(this.formData)
      this.visible = true
    },
    handleChangeTaskUnit(id) {
      getTaskUnit({ id }).then(taskUnit => {
        this.formData = cloneDeep({
          ...this.formData,
          ...taskUnit.data,
          taskRefId: id,
          taskTriggerType: 'manual'
        })
      })
    },
    handleSave() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        this.$refs.formRef2.validate(valid2 => {
          if (!valid2) return
          createTaskByUnit(this.formData)
            .then(() => {
              Message.success('保存成功')
              this.visible = false
              this.$emit('success')
            })
            .catch(() => {
              Message.error('保存失败')
            })
        })
      })
    },
    handleBeforeClose(done) {
      done()
    }
  }
}
</script>

<style scoped>
.task-drawer-dialog {
  top: 0 !important;
  margin: 0 !important;
  height: 100% !important;
  max-width: 720px !important;
  width: 720px !important;
  padding-bottom: 40px;
}
</style>
