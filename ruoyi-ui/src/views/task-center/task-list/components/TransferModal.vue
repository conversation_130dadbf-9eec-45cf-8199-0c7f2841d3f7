<template>
  <el-drawer title="转派" :visible.sync="visible" size="440px" :before-close="handleBeforeClose" :with-header="true"
    :destroy-on-close="true" :modal-append-to-body="true">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item label="任务归属" prop="type">
        <el-select v-model="formData.type" placeholder="请选择">
          <el-option label="组织" value="1" />
          <el-option label="人员" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="归属组织" prop="orgId">
        <ElTreeSelect v-model="formData.orgId" :options="orgTree" @change="orgChange"
          :props="{ label: 'orgName', children: 'children', checkStrictly: true, value: 'id', emitPath: false }"
          placeholder="请选择" />
      </el-form-item>

      <el-form-item v-if="formData.type === '2'" label="责任人" prop="userId" :show-message="true"
        :error="formData.orgId ? '' : '请先选择归属组织'">
        <el-select v-model="formData.userId" placeholder="请选择" :disabled="!formData.orgId" filterable clearable
          @change="userChange" style="width: 100%;">
          <el-option v-for="user in ownerUserList" :key="user.id" :label="user.orgName" :value="user.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="转派备注" prop="taskDesc">
        <el-input type="textarea" v-model="formData.taskDesc" placeholder="请输入" :rows="3" clearable />
      </el-form-item>



    </el-form>
    <div class="dialog-footer" style="text-align: right;">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">确认</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { transferTask } from '@/api/task'
import { getOrgTree } from '@/api/task-unit'
import { Message } from 'element-ui'
import { cloneDeep } from 'lodash'
import ElTreeSelect from '../../../../components/ElTreeSelect/index.vue'

export default {
  name: 'TransferModal',
  components: {
    ElTreeSelect
  },
  data() {
    return {
      visible: false,
      loading: false,
      formRef: null,
      defaultTransferData: {
        taskId: '',
        type: '1',
        orgId: '',
        userId: '',
        taskDesc: '',
        immediate: true,
        transferType: '1'
      },
      formData: {},
      formRules: {
        orgId: [
          { required: true, message: '请选择归属组织', trigger: 'change' }
        ],
        userId: [
          { required: true, message: '请选择责任人', trigger: 'change' }
        ]
      },
      orgTree: [],
      treeProps: {
        children: 'children',
        label: 'orgName'
      }
    }
  },
  computed: {
    ownerUserList() {
      if (!this.formData.orgId) return []
      const flatList = []
      function flatten(data) {
        data.forEach(item => {
          flatList.push(item)
          if (item.children) flatten(item.children)
        })
      }
      flatten(this.orgTree)
      const org = flatList.find(o => o.id === this.formData.orgId)
      return org && org.userlist ? org.userlist : []
    }
  },
  methods: {
    userChange() {
      if (this.formData.userId) {
        this.formData.userName = ownerUserList.find(el => el.id === this.formData.userId).orgName
      }
    },
    orgChange() {
      const flatList = []
      function flatten(data) {
        data.forEach(item => {
          flatList.push(item)
          if (item.children) flatten(item.children)
        })
      }
      flatten(this.orgTree)
      if (this.formData.orgId) {
        this.formData.orgName = flatten.find(el => el.id === this.formData.orgId).orgName
      }
    },
    async openModal(record) {
      try {
        const data = (await getOrgTree()).data
        this.orgTree = data
      } catch (error) {
        this.orgTree = []
      }

      this.formData = cloneDeep(this.defaultTransferData)
      this.formData.taskId = record.id
      this.formData.type = record.taskOwnerType || '1'
      this.formData.orgId = ''
      this.formData.userId = ''
      this.formData.taskDesc = ''
      this.visible = true
    },
    handleBeforeClose(done) {
      if (!this.loading) {
        done()
      }
    },
    handleSave() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return
        this.loading = true
        try {
          await transferTask(this.formData)
          Message.success('转派成功')
          this.visible = false
          this.$emit('success')
        } catch (error) {
          Message.error('转派失败')
        } finally {
          this.loading = false
        }
      })
    }
  }
}
</script>


<style lang="scss" scoped>
.dialog-footer {
  margin-right: 15px;
}
</style>
