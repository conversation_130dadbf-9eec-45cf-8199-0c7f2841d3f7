<template>
  <el-dialog title="完成" :visible.sync="visible" :close-on-click-modal="false" width="30%" v-if="visible"
    :before-close="handleBeforeClose">
    <el-popover placement="top" width="300" trigger="click" v-model="triggerVisible">
      <div class="history-content">
        <el-input v-model="historyKeyword" placeholder="搜索历史备注" clearable size="small" suffix-icon="el-icon-search" />
        <div class="history-scroll" style="max-height: 200px; overflow-y: auto; margin-top: 10px;">
          <template v-if="filteredHistory.length">
            <div class="history-item" v-for="(item, index) in filteredHistory" :key="index"
              @click="toTextarea(item.label)">
              {{ item.label }}
            </div>
          </template>
          <template v-else>
            <el-empty description="无历史数据"></el-empty>
          </template>
        </div>
      </div>
      <i slot="reference" class="el-icon-time history-icon" style="cursor: pointer; font-size: 20px;"></i>
    </el-popover>

    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" size="small">
      <el-form-item label="备注" prop="taskDesc">
        <el-input type="textarea" v-model="formData.taskDesc" placeholder="请输入" :autosize="{ minRows: 6, maxRows: 12 }"
          clearable />
      </el-form-item>

      <el-form-item label="任务统计量值" prop="workAmount" v-if="currentData && currentData.workAmountFlag === 1">
        <el-input-number v-model="formData.workAmount" :min="0" placeholder="请输入" style="width: 100%;" />
      </el-form-item>

      <!-- <el-collapse>
        <el-collapse-item title="更多设置" name="1">
          <el-form-item label="重点标记" prop="remind">
            <el-checkbox v-model="formData.remind" :disabled="!!formData.tipDate" />
          </el-form-item>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="提醒截止日期" prop="tipDate">
                <el-date-picker v-model="formData.tipDate" type="date" placeholder="请选择日期" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" :disabled-date="disabledDate" @change="tipDateChange" clearable
                  style="width: 100%;" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="仅当日" prop="tipType">
                <el-radio-group v-model="formData.tipType" size="small">
                  <el-radio :label="2">否</el-radio>
                  <el-radio :label="1">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="提醒信息" prop="tipMessage">
            <el-input type="textarea" v-model="formData.tipMessage" placeholder="请输入"
              :autosize="{ minRows: 2, maxRows: 4 }" clearable />
          </el-form-item>
        </el-collapse-item>
      </el-collapse> -->
    </el-form>

    <div slot="footer" class="dialog-footer" style="text-align: right;">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { completeTask, taskHisCompleteDesc } from '@/api/task'
import { Message } from 'element-ui'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'

export default {
  name: 'CompleteModal',
  data() {
    return {
      visible: false,
      triggerVisible: false,
      loading: false,
      historyKeyword: '',
      historyList: [],
      formData: cloneDeep(this.defaultCompleteData),
      currentData: null,
      rootId: '',
      formRules: {
        taskDesc: []
      },
      defaultCompleteData: {
        taskId: '',
        taskDesc: '',
        workAmount: 0,
        remind: false,
        tipDate: '',
        tipMessage: '',
        tipType: 1
      }
    }
  },
  computed: {
    filteredHistory() {
      if (!this.historyKeyword) return this.historyList
      return this.historyList.filter(item =>
        item.value.includes(this.historyKeyword)
      )
    }
  },
  methods: {
    async openModal(record, root) {
      this.formData = cloneDeep(this.defaultCompleteData)
      this.formData.taskId = record.id
      this.formData.taskDesc = ''
      this.formData.workAmount =
        record.workAmountFlag === 1 ? record.workAmount || 0 : 0
      this.formData.remind = false
      this.formData.tipDate = ''
      this.formData.tipMessage = ''
      this.formData.tipType = 1

      this.currentData = record
      this.rootId = root
      this.visible = true
      this.historyKeyword = ''
      try {
        const data = (await taskHisCompleteDesc({ refId: record.taskRefId })).data
        this.historyList = data.map((row) => ({
          label: row,
          value: row
        }))
      } catch (error) {
        this.historyList = []
      }
      this.setFormRules()
    },
    setFormRules() {
      // 如果当前时间 > 任务截止时间且不是同一天，备注必填
      if (
        !dayjs().startOf('day').isSame(dayjs(this.currentData?.taskEndTime).startOf('day')) &&
        dayjs().isAfter(dayjs(this.currentData?.taskEndTime))
      ) {
        this.formRules.taskDesc = [
          { required: true, message: '请输入完成备注', trigger: 'blur' }
        ]
      } else {
        this.formRules.taskDesc = []
      }
    },
    toTextarea(str) {
      this.formData.taskDesc = str
      this.triggerVisible = false
    },
    tipDateChange() {
      if (this.formData.tipDate) {
        this.formData.remind = true
      }
    },
    disabledDate(time) {
      return time.getTime() < dayjs().startOf('day').valueOf()
    },
    handleBeforeClose(done) {
      if (!this.loading) done()
    },
    handleSave() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return
        this.loading = true
        try {
          await completeTask({ ...this.formData, rootId: this.rootId })
          Message.success('操作成功')
          this.visible = false
          this.$emit('success')
        } catch (error) {
          Message.error('操作失败')
        } finally {
          this.loading = false
        }
      })
    }
  }
}
</script>

<style scoped>
.history-icon {
  position: absolute;
  top: 40px;
  right: 40px;
  z-index: 9999;
  cursor: pointer;
}

.history-content {
  padding: 10px;
  background: #fff;
  border: 1px solid #ebeef5;
}

.history-scroll {
  max-height: 200px;
  overflow-y: auto;
  margin-top: 10px;
}

.history-item {
  padding: 5px;
  border-bottom: 1px solid #ebeef5;
  word-break: break-word;
  cursor: pointer;
}

.history-item:hover {
  background-color: #f5f7fa;
}
</style>
