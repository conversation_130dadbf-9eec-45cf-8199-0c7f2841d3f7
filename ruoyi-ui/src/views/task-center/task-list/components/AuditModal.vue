<template>
  <el-dialog title="复核" :visible.sync="visible" :close-on-click-modal="false" @close="handleClose" width="600px"
    :before-close="handleBeforeClose">
    <div v-if="historyList && historyList.length > 0" style="position: relative; margin-bottom: 10px;">
      <el-popover placement="bottom-start" width="300" trigger="click" v-model="triggerVisible">
        <div class="history-content">
          <el-input v-model="historyKeyword" placeholder="搜索历史" size="small" clearable />
          <div class="history-scroll">
            <template v-if="filteredHistory.length > 0">
              <div class="history-item" v-for="(item, index) in filteredHistory" :key="index"
                @click="toTextarea(item.label)">
                {{ item.label }}
              </div>
            </template>
            <template v-else>
              <el-empty description="无数据" />
            </template>
          </div>
        </div>
        <i slot="reference" class="el-icon-time history-icon" style="cursor: pointer;"></i>
      </el-popover>
    </div>

    <el-collapse v-model="collapseActive" accordion>
      <el-collapse-item title="经办备注" name="1">
        <div class="description-line">
          <div class="description-line-content">{{ taskCompleteDesc }}</div>
        </div>
      </el-collapse-item>
    </el-collapse>

    <el-form ref="formRef" :model="formData" :rules="formRules" label-position="top" size="small">
      <el-form-item label="备注" prop="taskDesc">
        <el-input type="textarea" v-model="formData.taskDesc" placeholder="请输入" :rows="6" clearable />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { auditTask, batchTaskAudit, taskHisCheckDesc } from '@/api/task'

export default {
  name: 'AuditModal',
  data() {
    return {
      visible: false,
      triggerVisible: false,
      historyKeyword: '',
      historyList: [],
      taskCompleteDesc: '',
      formData: {
        taskId: '',
        taskDesc: '',
        taskIds: []
      },
      formRules: {},
      collapseActive: ['1']
    }
  },
  computed: {
    filteredHistory() {
      if (!this.historyKeyword) {
        return this.historyList
      }
      return this.historyList.filter(item =>
        item.value.includes(this.historyKeyword)
      )
    }
  },
  methods: {
    async openModal(record) {
      this.taskCompleteDesc = record.taskCompleteDesc || ''
      this.formData = {
        taskIds: [],
        taskId: record.id,
        taskDesc: ''
      }
      this.visible = true

      if (!record.taskRefId) {
        this.historyList = []
        return
      }
      const history = (await taskHisCheckDesc({ refId: record.taskRefId })).data
      this.historyList = history.map(row => ({
        label: row,
        value: row
      }))
    },
    openBatchModal(ids) {
      this.formData = {
        taskId: '',
        taskIds: ids,
        taskDesc: ''
      }
      this.visible = true
    },
    toTextarea(str) {
      this.formData.taskDesc = str
      this.triggerVisible = false
    },
    handleCancel() {
      this.visible = false
    },
    handleClose() {
      this.triggerVisible = false
      this.historyKeyword = ''
    },
    handleBeforeClose(done) {
      this.handleClose()
    },
    async handleSave() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return
        try {
          if (this.formData.taskId) {
            await auditTask(this.formData)
          } else {
            await batchTaskAudit(this.formData)
          }
          this.$message.success('复核成功')
          this.visible = false
          this.$emit('success')
        } catch (err) {
          this.$message.error('复核失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.description-line {
  padding: 6px;
  background: rgb(247, 248, 250);
  border-radius: 4px;
  margin-bottom: 10px;
}

.description-line-content {
  margin-top: 10px;
  color: #888;
  white-space: pre-wrap;
}

.history-icon {
  position: absolute;
  bottom: 50px;
  left: 70px;
  z-index: 9999;
  cursor: pointer;
}

.history-content {
  width: 300px;
  padding: 10px;
  background: #fff;
  border: 1px solid #ebebeb;
}

.history-scroll {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 10px;
}

.history-item {
  padding: 5px;
  margin-top: 5px;
  border-bottom: 1px solid #ebebeb;
  word-break: break-word;
  cursor: pointer;
}

.history-item:last-child {
  border: none;
}

.history-item:hover {
  background: #f2f3f5;
}
</style>
