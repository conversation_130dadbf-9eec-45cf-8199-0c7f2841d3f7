<template>
  <el-drawer title="编辑任务单元" :visible.sync="visible" size="760px" append-to-body :wrapperClosable="false"
    custom-class="task-unit-drawer">
    <!-- 表单组件 -->
    <task-unit-edit-form ref="formRef" :key="activeKey" :formData.sync="formData" :task-list="taskList"
      :is-temp="true" />
    <div class="drawer-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </div>
  </el-drawer>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import TaskUnitEditForm from '../../task-unit/components/TaskUnitEditForm.vue'

export default {
  name: 'TaskUnitEditDrawer',
  components: {
    TaskUnitEditForm
  },
  props: {
    taskList: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      visible: false,
      formRef: null,
      formData: this.getDefaultTaskUnit(),
      activeKey: -1
    }
  },
  methods: {
    // 默认数据
    getDefaultTaskUnit() {
      return {
        id: '',
        parentId: '',
        taskName: '',
        taskType: 'daily',
        taskTriggerType: 'manual',
        taskTriggerId: '',
        taskCronVal: '1',
        taskDesc: '',
        taskCompleteType: 'manual',
        taskCompleteUnitId: '',
        taskAuditType: '0',
        taskAuditUnitId: '',
        taskWarnNotice: 'web',
        taskPriority: '2',
        taskLevel: '2',
        taskAttachmentsType: '0',
        taskOwnerType: '1',
        taskOwnerId: '',
        ownerOrgId: '',
        taskOwnerVal: '',
        taskCheckReq: '0',
        taskCheckType: '1',
        taskCheckId: '',
        checkOrgId: '',
        taskCheckVal: '',
        taskStartTime: '',
        taskEndTime: '',
        taskStartThreshold: 0,
        taskEndThreshold: 0,
        taskTags: '',
        taskAuthType: '1',
        taskAuthId: '',
        taskAuthVal: '',
        taskAuthScope: 'ALL',
        taskDeferredType: '0',
        taskDeferredCount: 0,
        dependOnIds: '',
        requiredItem: '',
        accessLevel: 0,
        workAmountFlag: 0,
        workAmount: 0,
        taskNameAppend: 0,
        taskAppendType: 1,
        taskCreateType: 0,
        contentParse: 0,
        dateDurType: 0,
        taskReportFlag: 0,
        taskReportCount: 0,
        tipType: 0
      }
    },

    // 打开弹窗方法（供父组件调用）
    openModal(record) {
      if (!record) {
        this.formData = this.getDefaultTaskUnit()
      } else {
        this.formData = cloneDeep(record)
        if (this.formData.dependOnIds) {
          this.formData.dependOnIds = this.formData.dependOnIds.split(',')
        }
      }
      this.visible = true
      this.activeKey++
    },
    async handleSave() {
      try {
        const valid = await this.$refs.formRef.validate()
        if (!valid) return

        if (Array.isArray(this.formData.dependOnIds)) {
          this.formData.dependOnIds = this.formData.dependOnIds.join(',')
        }

        this.$emit('success', this.formData)
        this.visible = false
      } catch (e) {
        console.error('验证失败', e)
      }
    },

    // 保存逻辑（点击关闭前执行）
    async handleBeforeClose(done) {
      try {
        const valid = await this.$refs.formRef.validate()
        if (!valid) return

        if (Array.isArray(this.formData.dependOnIds)) {
          this.formData.dependOnIds = this.formData.dependOnIds.join(',')
        }

        this.$emit('success', this.formData)
        this.visible = false
      } catch (e) {
        console.error('验证失败', e)
      }
    }
  }
}
</script>

<style scoped>
.task-unit-drawer ::v-deep .el-drawer__body {
  box-sizing: border-box;
  padding: 20px;
}

.drawer-footer {
  text-align: right;
  margin-right: 15px;
  margin-bottom: 15px;
  border-top: 1px solid #ebeef5;
}
</style>
