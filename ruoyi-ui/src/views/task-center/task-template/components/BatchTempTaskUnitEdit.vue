<template>
  <el-dialog title="编辑任务单元" :visible.sync="visible" width="720px" :close-on-click-modal="false" @close="handleClose"
    append-to-body>
    <task-unit-edit-form ref="formRef" :form-data.sync="formData" :batch-edit="true" :is-temp="true"
      @update:formData="onFormDataUpdate" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import TaskUnitEditForm from '../../task-unit/components/TaskUnitEditForm.vue'

export default {
  name: 'TaskUnitEditDialog',
  components: {
    TaskUnitEditForm,
  },
  data() {
    return {
      visible: false,
      formData: {
        taskType: '',
        taskTriggerType: '',
        taskTriggerId: '',
        taskCronVal: '',
        taskCompleteType: '',
        taskCompleteUnitId: '',
        taskAuditType: '',
        taskAuditUnitId: '',
        taskWarnNotice: '',
        taskPriority: '',
        taskLevel: '',
        taskAttachmentsType: '',
        taskOwnerType: '',
        taskOwnerId: '',
        ownerOrgId: '',
        taskOwnerVal: '',
        taskCheckReq: '',
        taskCheckType: '',
        taskCheckId: '',
        checkOrgId: '',
        taskCheckVal: '',
        taskStartTime: '',
        taskEndTime: '',
        taskStartThreshold: undefined,
        taskEndThreshold: undefined,
        taskTags: '',
        taskAuthType: '',
        taskAuthId: '',
        taskAuthVal: '',
        taskAuthScope: '',
        taskDeferredType: '',
        taskDeferredCount: undefined,
        requiredItem: '',
        accessLevel: undefined,
      },
      formRules: {
        taskOwnerId: [
          { required: true, message: '请选择责任人', trigger: 'change' },
        ],
        ownerOrgId: [
          { required: true, message: '请选择归属组织', trigger: 'change' },
        ],
        taskCheckId: [
          { required: true, message: '请选择复核岗', trigger: 'change' },
        ],
        checkOrgId: [
          { required: true, message: '请选择复核人', trigger: 'change' },
        ],
      },
    }
  },
  methods: {
    openModal() {
      this.formData = cloneDeep({
        taskType: '',
        taskTriggerType: '',
        taskTriggerId: '',
        taskCronVal: '',
        taskCompleteType: '',
        taskCompleteUnitId: '',
        taskAuditType: '',
        taskAuditUnitId: '',
        taskWarnNotice: '',
        taskPriority: '',
        taskLevel: '',
        taskAttachmentsType: '',
        taskOwnerType: '',
        taskOwnerId: '',
        ownerOrgId: '',
        taskOwnerVal: '',
        taskCheckReq: '',
        taskCheckType: '',
        taskCheckId: '',
        checkOrgId: '',
        taskCheckVal: '',
        taskStartTime: '',
        taskEndTime: '',
        taskStartThreshold: undefined,
        taskEndThreshold: undefined,
        taskTags: '',
        taskAuthType: '',
        taskAuthId: '',
        taskAuthVal: '',
        taskAuthScope: '',
        taskDeferredType: '',
        taskDeferredCount: undefined,
        requiredItem: '',
        accessLevel: undefined,
      })
      this.visible = true
    },

    handleSave() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return

        // 清理空值字段
        Object.keys(this.formData).forEach(key => {
          if (this.formData[key] === '' || this.formData[key] === undefined) {
            delete this.formData[key]
          }
        })

        this.$emit('success', this.formData)
        this.visible = false
      })
    },

    handleClose() {
      this.visible = false
    },

    // 处理从子组件更新的formData（如果需要双向绑定）
    onFormDataUpdate(newVal) {
      this.formData = newVal
    },

    afterTaskOwnerTypeChange(val) {
      if (val === '1') {
        this.formData.taskOwnerId = this.formData.ownerOrgId
      } else {
        this.formData.taskOwnerId = ''
      }
    },

    afterOwnerOrgIdChange(val) {
      if (this.formData.taskOwnerType === '1') {
        this.formData.taskOwnerId = val
      }
    },

    afterTaskCheckTypeChange(val) {
      if (val === '1') {
        this.formData.taskCheckId = this.formData.checkOrgId
      } else {
        this.formData.taskCheckId = ''
      }
    },

    afterCheckOrgIdChange(val) {
      if (this.formData.taskCheckType === '1') {
        this.formData.taskCheckId = val
      }
    },
  },
}
</script>
