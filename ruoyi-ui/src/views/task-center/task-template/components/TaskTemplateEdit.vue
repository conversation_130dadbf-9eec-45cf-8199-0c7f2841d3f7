<template>
  <el-drawer v-if="formData" :title="formData.id ? '编辑任务模板' : '新建任务模板'" :visible.sync="visible" size="960px"
    :with-header="true" append-to-body destroy-on-close :before-close="onClose">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-row v-if="formData.id">
        <el-col :span="12">
          <el-form-item prop="id" label="模板id">
            <span>{{ formData.id }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="templateName" label="模板名称">
            <el-input v-model="formData.templateName" clearable placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="templateType" label="模板类型">
            <el-select style="width:100%" v-model="formData.templateType" placeholder="请选择">
              <el-option label="日常任务" value="1" />
              <el-option label="临时任务" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="schedulerType" label="生成周期" :label-tooltip="{ content: '覆盖任务清单中的触发类型' }">
            <el-select style="width:100%" v-model="formData.schedulerType" placeholder="请选择">
              <el-option label="立即生成" value="manual" />
              <el-option label="工作日" value="daily" />
              <el-option label="自定义" value="dynamic" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="orgId" label="归属组织">
            <ElTreeSelect v-model="formData.orgId" :options="orgTree"
              :props="{ value: 'id', label: 'orgName', children: 'children' }" placeholder="请选择" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.schedulerType === 'dynamic'">
        <el-col :span="12">
          <el-form-item prop="triggerId" label="触发器">
            <el-select style="width:100%" v-model="formData.triggerId" filterable placeholder="请选择">
              <el-option v-for="item in tradeTypeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="cronVal" label="触发频率">
            <el-select style="width:100%" v-model="formData.cronVal" placeholder="请选择">
              <el-option label="1小时" value="1" />
              <el-option label="2小时" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="tempSort" label="排序">
            <el-input-number v-model="formData.tempSort" :min="0" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="task-select-wrap">
      <!-- 任务单元列表 -->
      <div class="task-panel">
        <div class="task-panel-title">任务单元列表</div>
        <div class="task-panel-search-wrap">
          <template v-if="taskBatchFlag">
            <el-dropdown trigger="hover">
              <el-button type="text">选择</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="batchSelect(1)">全选</el-dropdown-item>
                <el-dropdown-item @click.native="batchSelect(2)">反选</el-dropdown-item>
                <el-dropdown-item @click.native="batchSelect(0)">取消选中</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button type="text" :disabled="!selectTaskList.length" @click="addTasks(selectTaskList)">添加({{
              selectTaskList.length }})</el-button>
            <el-button type="text" @click="taskBatchFlag = false">退出</el-button>
          </template>
          <template v-else>
            <el-input v-model="searchTaskName" placeholder="搜索" clearable size="small" @input="filterTasks" />
            <el-tooltip content="批量操作">
              <i class="el-icon-s-operation" @click="taskBatchFlag = true" />
            </el-tooltip>
          </template>
        </div>
        <el-tree class="task-panel-body" ref="taskTreeRef" :data="filterTaskUnitList" node-key="id" show-checkbox
          default-expand-all :props="treeProps" :highlight-current="false" :check-strictly="true"
          @check-change="onTaskCheckChange">
          <div class="custom-tree-node" slot-scope="{ node }">
            <div class="custom-node-text">{{ node.data.taskName }}</div>
            <i class="el-icon-plus custom-node-icon" @click.stop="addTasks([node.data.id])" />
          </div>
        </el-tree>
      </div>

      <!-- 模板包含任务单元 -->
      <div class="selected-task-panel">
        <div class="task-panel-title">模板包含任务单元</div>
        <div class="task-panel-search-wrap">
          <template v-if="tempTaskBatchFlag">
            <el-dropdown trigger="hover">
              <el-button type="text">选择</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="batchTempSelect(1)">全选</el-dropdown-item>
                <el-dropdown-item @click.native="batchTempSelect(2)">反选</el-dropdown-item>
                <el-dropdown-item @click.native="batchTempSelect(0)">取消选中</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button type="text" :disabled="!selectTempTaskList.length" @click="openBatchEdit">编辑({{
              selectTempTaskList.length }})</el-button>
            <el-popconfirm title="你确定要删除吗?" @confirm="deleteTempTasks">
              <el-button type="text" slot="reference" :disabled="!selectTempTaskList.length">删除({{
                selectTempTaskList.length }})</el-button>
            </el-popconfirm>
            <el-button type="text" @click="toLinearDependency" :disabled="!selectTempTaskList.length">线性依赖</el-button>
            <el-button type="text" @click="tempTaskBatchFlag = false">退出</el-button>
          </template>
          <template v-else>
            <el-input v-model="searchTempTaskName" placeholder="搜索" clearable size="small" @input="filterTempTree" />
            <el-tooltip content="展开/关闭">
              <i class="el-icon-c-scale-to-original" @click="toggleExpand" />
            </el-tooltip>
            <!-- <el-tooltip content="批量操作">
              <i class="el-icon-s-operation" @click="tempTaskBatchFlag = true" />
            </el-tooltip> -->
          </template>
        </div>
        <el-tree class="task-panel-body" ref="templateTreeRef" :data="filterTempTaskTree" node-key="id" show-checkbox
          default-expand-all draggable :props="treeProps" :check-strictly="true" @node-click="onTempNodeClick"
          @node-drag-start="onTempDragStart" @node-drop="onTempDrop">
          <span class="custom-tree-node" slot-scope="{ node }">
            <div class="custom-node-text">{{ node.data.taskName }}</div>
            <el-dropdown trigger="click" @command="onNodeCommand(node.data, $event)">
              <i class="el-icon-more custom-node-icon" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                <el-dropdown-item command="delete" style="color:#f56c6c">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </el-tree>
      </div>
    </div>

    <div class="drawer-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <!-- 子组件 -->
    <TempTaskUnitEdit ref="tempEdit" :task-list="formData.list" @success="onEditSuccess" />
    <BatchTempTaskUnitEdit ref="batchEdit" @success="onBatchSuccess" />
  </el-drawer>
</template>

<script>
import { cloneDeep, isEqual } from 'lodash';
import { getTaskUnitList, getOrgTree } from '@/api/task-unit';
import { getTradeTypeList } from '@/api/trade-type';
import { getTaskTemplate, saveTaskTemplate } from '@/api/task-template';
import TempTaskUnitEdit from './TempTaskUnitEdit.vue';
import BatchTempTaskUnitEdit from './BatchTempTaskUnitEdit.vue';
import ElTreeSelect from '../../../../components/ElTreeSelect/index.vue'

export default {
  name: 'TaskTemplateEdit',
  components: { TempTaskUnitEdit, BatchTempTaskUnitEdit, ElTreeSelect },
  data() {
    return {
      formRef: null,
      visible: false,
      orgTree: [],
      tradeTypeList: [],
      taskUnitList: [],
      defaultFormData: {
        templateName: '', templateType: '1', schedulerType: 'manual', triggerId: '', cronVal: '1', orgId: '', tempSort: 0, list: []
      },
      formData: cloneDeep(this.defaultFormData),
      cacheFormData: {},
      searchTaskName: '',
      searchTempTaskName: '',
      taskBatchFlag: false,
      tempTaskBatchFlag: false,
      selectTaskList: [],
      selectTempTaskList: [],
      expandedTemplate: true,
      treeProps: { children: 'children', label: 'taskName' },
      formRules: {
        templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        orgId: [{ required: true, message: '请选择归属组织', trigger: 'change' }],
        tempSort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
      }
    };
  },
  computed: {
    filterTaskUnitList() {
      return this.searchTaskName
        ? this.taskUnitList.filter(u => u.taskName.includes(this.searchTaskName))
        : this.taskUnitList;
    },
    filterTempTaskTree() {
      const traverse = (nodes) => {
        return nodes.filter(n => n.taskName.includes(this.searchTempTaskName))
          .map(n => ({ ...n, children: traverse(n.children || []) }));
      };
      return this.searchTempTaskName ? traverse(this.formData.list) : this.formData.list;
    }
  },
  methods: {
    async openModal(record) {
      console.log(record)
      this.visible = true
      this.taskUnitList = (await getTaskUnitList()).data;
      this.tradeTypeList = (await getTradeTypeList()).data;
      this.orgTree = (await getOrgTree()).data;
      if (record && record.id) {
        const d = (await getTaskTemplate({ id: record.id }))
        this.formData = d;
      } else {
        this.formData = cloneDeep(this.defaultFormData)
      }
      this.cacheFormData = cloneDeep(this.formData);
    },
    onClose(done) {
      if (!isEqual(this.formData, this.cacheFormData)) {
        this.$confirm('当前任务模板未保存，确定退出？', '提示', { type: 'warning' })
          .then(() => this.closeDrawer());
      } else this.closeDrawer();
    },
    closeDrawer() {
      this.visible = false;
      this.$emit('update:visible', false);
    },
    filterTasks() { },
    filterTempTree() { },
    batchSelect(type) {
      const ids = this.filterTaskUnitList.map(u => u.id);
      if (type === 1) this.selectTaskList = ids;
      else if (type === 2) this.selectTaskList = ids.filter(id => !this.selectTaskList.includes(id));
      else this.selectTaskList = [];
    },
    batchTempSelect(type) {
      const ids = this.formData.list.map(u => u.id);
      if (type === 1) this.selectTempTaskList = ids;
      else if (type === 2) this.selectTempTaskList = ids.filter(id => !this.selectTempTaskList.includes(id));
      else this.selectTempTaskList = [];
    },
    addTasks(ids) {
      ids.forEach(id => {
        const u = this.taskUnitList.find(t => t.id === id);
        if (u) this.formData.list.push({ ...u, id: Date.now().toString() });
      });
      this.selectTaskList = [];
    },
    onTaskCheckChange(data, checked) {
      this.selectTaskList = this.$refs.taskTreeRef.getCheckedKeys();
    },
    openBatchEdit() { this.$refs.batchEdit.openModal(); },
    onTempNodeClick(node) { },
    onNodeCommand(data, command) {
      console.log(data)
      if (command === 'edit') this.$refs.tempEdit.openModal(data);
      else if (command === 'delete') this.deleteTempTasks([data.id]);
    },
    deleteTempTasks(ids) {
      const collect = (nodes, remove) => {
        nodes.forEach(n => {
          if (remove.includes(n.id)) remove = remove.concat((n.children || []).map(c => c.id));
          if (n.children) collect(n.children, remove);
        });
        return remove;
      };
      const toRemove = collect(this.filterTempTaskTree, [...ids]);
      this.formData.list = this.formData.list.filter(u => !toRemove.includes(u.id));
      this.selectTempTaskList = [];
    },
    onEditSuccess(updated) {
      const idx = this.formData.list.findIndex(u => u.id === updated.id);
      if (idx >= 0) this.$set(this.formData.list, idx, updated);
    },
    onBatchSuccess(data) {
      this.formData.list.forEach(u => {
        if (this.selectTempTaskList.includes(u.id)) Object.assign(u, data);
      });
    },
    toLinearDependency() {
      const list = this.formData.list;
      this.selectTempTaskList.forEach(taskId => {
        const selected = list.find(item => item.id === taskId);
        if (!selected) return;
        const parentId = selected.parentId;
        // 如果该任务无子节点且同父节点下其他节点存在
        const hasChildren = list.some(item => item.parentId === taskId);
        const hasSiblings = list.some(item => item.parentId === parentId && item.id !== taskId);
        if (!hasChildren && hasSiblings) {
          const index = list.findIndex(item => item.id === taskId);
          for (let i = index - 1; i >= 0; i--) {
            if (list[i].parentId === parentId) {
              // 建立线性依赖
              this.$set(selected, 'dependOnIds', list[i].id);
              break;
            }
          }
        }
      });
    },
    handleSave() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        let list = []
        console.log(this.formData.list)
        function d(l, parentId) {
          if (l && l.length > 0) {
            for (let s of l) {
              list.push({ ...s, parentId })
              d(s.children, s.id)
            }
          }
        }
        d(this.formData.list, "0")
        console.log(list)
        saveTaskTemplate({ ...this.formData, list }).then(() => {
          this.$message.success('保存成功');
          this.visible = false;
          this.$emit('update:visible', false);
          this.$emit('success');
        });
      });
    },
    onTempDragStart(node, event) {
      this.dragTempNode = node.data;
    },
    // 处理放下逻辑
    onTempDrop(draggingNode, dropNode, dropType, e) {
      const list = this.formData.list;
      const drag = this.dragTempNode;
      const drop = dropNode.data;
      console.log(this.formData.list, drag, dropNode, dropType)
      return

      // 新增节点
      if (!list.some(item => item.id === drag.id)) {
        const newNode = cloneDeep(drag);
        newNode.id = Date.now().toString();
        if (dropType === 'inner') {
          newNode.parentId = drop.id;
          list.push(newNode);
        } else {
          newNode.parentId = drop.parentId;
          const idx = list.findIndex(item => item.id === drop.id);
          if (dropType === 'before') list.splice(idx, 0, newNode);
          else list.splice(idx + 1, 0, newNode);
        }
      } else {
        console.log(dropType)

        // 重新排序
        if (!this.selectTempTaskList.includes(drag.id)) {
          const srcIdx = list.findIndex(item => item.id === drag.id);
          list.splice(srcIdx, 1);
          if (dropType === 'inner') {
            drag.parentId = drop.id;
            list.push(drag);
          } else {
            drag.parentId = drop.parentId;
            const idx = list.findIndex(item => item.id === drop.id);
            if (dropType === 'before') list.splice(idx, 0, drag);
            else list.splice(idx + 1, 0, drag);
          }
        }
        // 多选拖拽可按需扩展
      }
      console.log(this.formData.list)
      this.dragTempNode = null;
    },
    toggleExpand() {
      const tree = this.$refs.templateTreeRef;
      const root = tree.store.root;
      const expand = !this.expandedTemplate;
      root.childNodes.forEach(node => { node.expanded = expand; });
      this.expandedTemplate = expand;
    },
  }
};
</script>

<style scoped lang="scss">
.task-template-edit-wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.task-select-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
  box-sizing: border-box;
  padding: 16px;
}

.task-panel,
.selected-task-panel {
  border: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
}

.task-panel-title {
  padding: 12px;
  font-weight: bold;
}

.task-panel-search-wrap {
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-panel-body {
  flex: 1;
  overflow: auto;
  padding: 0 12px;
}

.drawer-footer {
  text-align: right;
  margin-top: 16px;
  margin-right: 15px;
  margin-bottom: 15px;
}

.custom-tree-node {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.custom-node-text {
  width: 330px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.custom-node-icon {
  margin-left: 8px;
  cursor: pointer;
}
</style>
