# 自动审批功能快速测试指南

## 🚀 快速开始

### 1. 编译项目
```bash
mvn clean compile
```

### 2. 创建测试流程

#### 简单测试BPMN配置：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://flowable.org/test">

  <process id="autoApprovalTest" name="自动审批测试流程">
    
    <startEvent id="start" />
    
    <userTask id="testApproval" name="测试审批">
      <extensionElements>
        <flowable:taskListener event="create" 
            class="com.ruoyi.flowable.listener.FieldAutoApprovalTaskListener">
          
          <!-- 条件自动审批：金额小于1000自动通过 -->
          <flowable:field name="approvalType">
            <flowable:string>conditional</flowable:string>
          </flowable:field>
          
          <flowable:field name="condition">
            <flowable:expression>${amount < 1000}</flowable:expression>
          </flowable:field>
          
          <flowable:field name="comment">
            <flowable:string>金额小于1000元，系统自动审批通过</flowable:string>
          </flowable:field>
          
        </flowable:taskListener>
      </extensionElements>
    </userTask>
    
    <endEvent id="end" />
    
    <sequenceFlow id="flow1" sourceRef="start" targetRef="testApproval" />
    <sequenceFlow id="flow2" sourceRef="testApproval" targetRef="end" />
    
  </process>
</definitions>
```

### 3. 测试代码

```java
@Service
public class AutoApprovalTestService {
    
    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private TaskService taskService;
    
    /**
     * 测试自动审批 - 应该自动通过
     */
    public void testAutoApprovalSuccess() {
        // 设置金额为800，小于1000，应该自动审批
        Map<String, Object> variables = new HashMap<>();
        variables.put("amount", 800);
        variables.put("applicant", "张三");
        
        ProcessInstance processInstance = runtimeService
            .startProcessInstanceByKey("autoApprovalTest", variables);
        
        System.out.println("流程启动：" + processInstance.getId());
        
        // 检查是否还有待办任务（应该没有，因为自动审批了）
        List<Task> tasks = taskService.createTaskQuery()
            .processInstanceId(processInstance.getId())
            .list();
        
        if (tasks.isEmpty()) {
            System.out.println("✅ 测试成功：任务已自动审批完成");
        } else {
            System.out.println("❌ 测试失败：任务未自动审批，还有 " + tasks.size() + " 个待办任务");
        }
    }
    
    /**
     * 测试自动审批 - 不应该自动通过
     */
    public void testAutoApprovalFail() {
        // 设置金额为1500，大于1000，不应该自动审批
        Map<String, Object> variables = new HashMap<>();
        variables.put("amount", 1500);
        variables.put("applicant", "李四");
        
        ProcessInstance processInstance = runtimeService
            .startProcessInstanceByKey("autoApprovalTest", variables);
        
        System.out.println("流程启动：" + processInstance.getId());
        
        // 检查是否有待办任务（应该有，因为不满足自动审批条件）
        List<Task> tasks = taskService.createTaskQuery()
            .processInstanceId(processInstance.getId())
            .list();
        
        if (!tasks.isEmpty()) {
            System.out.println("✅ 测试成功：条件不满足，任务等待人工审批");
        } else {
            System.out.println("❌ 测试失败：任务意外自动审批了");
        }
    }
}
```

## 🧪 测试场景

### 场景1：无条件自动审批
```xml
<flowable:field name="approvalType">
  <flowable:string>unconditional</flowable:string>
</flowable:field>
<flowable:field name="comment">
  <flowable:string>确认任务自动通过</flowable:string>
</flowable:field>
```

**预期结果**：任务立即自动完成

### 场景2：条件自动审批（满足条件）
```xml
<flowable:field name="approvalType">
  <flowable:string>conditional</flowable:string>
</flowable:field>
<flowable:field name="condition">
  <flowable:expression>${amount < 1000}</flowable:expression>
</flowable:field>
```

**测试数据**：`amount = 800`
**预期结果**：任务自动完成

### 场景3：条件自动审批（不满足条件）
```xml
<flowable:field name="approvalType">
  <flowable:string>conditional</flowable:string>
</flowable:field>
<flowable:field name="condition">
  <flowable:expression>${amount < 1000}</flowable:expression>
</flowable:field>
```

**测试数据**：`amount = 1500`
**预期结果**：任务等待人工审批

### 场景4：禁用自动审批
```xml
<flowable:field name="approvalType">
  <flowable:string>disabled</flowable:string>
</flowable:field>
```

**预期结果**：任务等待人工审批

## 📋 检查清单

### 部署前检查：
- [ ] 项目编译成功
- [ ] 监听器类存在：`FieldAutoApprovalTaskListener`
- [ ] BPMN文件配置正确
- [ ] 字段名称拼写正确

### 运行时检查：
- [ ] 流程成功启动
- [ ] 监听器被触发（查看日志）
- [ ] 条件表达式执行正确
- [ ] 任务状态符合预期

### 日志检查：
```
# 成功的日志示例：
INFO - 检测到自动审批任务: 测试审批, 类型: conditional
DEBUG - 执行条件表达式: ${amount < 1000}, 任务: 测试审批
DEBUG - 条件表达式结果: true, 表达式: ${amount < 1000}
INFO - 条件审批结果: true, 任务: 测试审批
INFO - 自动审批完成，任务: 测试审批, 审批人: system, 意见: 金额小于1000元，系统自动审批通过
```

## 🔍 故障排除

### 1. 监听器未触发
- 检查类名是否正确
- 确认事件类型为 `create`
- 查看流程部署是否成功

### 2. 条件表达式错误
- 检查语法是否正确
- 确认变量是否存在
- 使用简单条件测试

### 3. 服务注入失败
- 查看SpringUtils是否可用
- 检查Spring容器状态
- 查看详细错误日志

### 4. 任务未完成
- 检查TaskService是否正常
- 确认用户权限设置
- 查看任务状态

## 💡 调试技巧

1. **启用DEBUG日志**：
```yaml
logging:
  level:
    com.ruoyi.flowable.listener: DEBUG
```

2. **使用简单条件测试**：
```xml
<flowable:field name="condition">
  <flowable:expression>${true}</flowable:expression>
</flowable:field>
```

3. **打印变量值**：
```xml
<flowable:field name="comment">
  <flowable:expression>测试：金额=${amount}，申请人=${applicant}</flowable:expression>
</flowable:field>
```

按照这个指南测试，可以快速验证自动审批功能是否正常工作！
