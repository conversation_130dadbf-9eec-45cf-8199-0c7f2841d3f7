# 公共值管理功能说明

## 功能概述

公共值管理功能是一个用于管理合同模板中公共参数值的系统，支持文本和文件两种类型的公共值管理。

## 功能特性

1. **公共值增删改查**：支持公共值的完整CRUD操作
2. **分类管理**：支持按产品类型和模板分类进行管理
3. **多种类型支持**：支持文本和文件两种类型的公共值
4. **文件预览**：支持Word文档的在线预览
5. **查询接口**：提供按产品类型和模板分类查询的接口

## 数据库表结构

### con_common_value 表

```sql
CREATE TABLE `con_common_value` (
  `id` bigint(20) NOT NULL COMMENT '雪花算法生成的ID',
  `product_type` varchar(100) DEFAULT NULL COMMENT '归属产品类型',
  `template_category` varchar(100) DEFAULT NULL COMMENT '归属模板分类',
  `name` varchar(255) NOT NULL COMMENT '公共值名称',
  `value_content` longtext COMMENT '公共值内容 (对于简单文本) 或 文件路径 (对于文件)',
  `value_type` varchar(10) NOT NULL DEFAULT 'TEXT' COMMENT '公共值类型: FILE (文件路径) 或 TEXT (纯文本)',
  `html_content` longtext COMMENT 'HTML内容，用于预览显示',
  `remarks` text COMMENT '备注',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(64) DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公共值管理表';
```

## 后端API接口

### 1. 分页查询公共值列表
- **URL**: `GET /api/common-values/page`
- **参数**: 
  - `current`: 当前页（默认1）
  - `size`: 每页大小（默认10）
  - `productType`: 产品类型（可选）
  - `templateCategory`: 模板分类（可选）
  - `name`: 公共值名称（可选）

### 2. 根据产品类型和模板分类查询
- **URL**: `GET /api/common-values/query`
- **参数**:
  - `productType`: 产品类型（可选）
  - `templateCategory`: 模板分类（可选）

### 3. 获取公共值详情
- **URL**: `GET /api/common-values/{id}`
- **参数**: `id` - 公共值ID

### 4. 新增公共值
- **URL**: `POST /api/common-values`
- **请求体**: CommonValueDTO对象

### 5. 更新公共值
- **URL**: `PUT /api/common-values/{id}`
- **参数**: `id` - 公共值ID
- **请求体**: CommonValueDTO对象

### 6. 删除公共值
- **URL**: `DELETE /api/common-values/{id}`
- **参数**: `id` - 公共值ID

## 前端页面

### 主要页面
1. **公共值管理页面**: `/contract-management/common-value/index`
2. **测试页面**: `/contract-management/common-value-test/index`

### 组件说明
1. **CommonValueModal**: 新增/编辑公共值弹窗
2. **CommonValueViewModal**: 查看公共值详情弹窗
3. **WordModal**: Word文档预览弹窗（复用现有组件）

## 部署说明

### 1. 数据库初始化
执行SQL脚本：`script/sql/mysql/mysql_common_value.sql`

### 2. 后端部署
确保以下文件已添加到项目中：
- `CommonValue.java` - 实体类
- `CommonValueMapper.java` - Mapper接口
- `CommonValueService.java` - 服务类
- `CommonValueController.java` - 控制器
- `CommonValueDTO.java` - 数据传输对象
- `CommonValueVO.java` - 视图对象

### 3. 前端部署
确保以下文件已添加到项目中：
- `commonValue.js` - API调用文件
- `index.vue` - 主页面
- `CommonValueModal.vue` - 新增/编辑弹窗
- `CommonValueViewModal.vue` - 查看弹窗
- `common-value-test.vue` - 测试页面

### 4. 路由配置
路由已添加到 `router/index.js` 中的动态路由部分。

## 测试方法

1. 访问测试页面：`/contract-management/common-value-test/index`
2. 点击各个测试按钮验证API功能
3. 点击"进入公共值管理"按钮进入主功能页面

## 注意事项

1. 文件上传功能依赖现有的模板上传接口
2. 文件预览功能依赖现有的Word预览组件
3. 用户权限和认证依赖现有的权限系统
4. 数据库连接和配置使用现有的配置

## 扩展功能

后续可以考虑添加：
1. 公共值模板功能
2. 批量导入/导出功能
3. 版本管理功能
4. 审批流程功能
