package com.ruoyi.contract.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.contract.domain.ContractVersionHistory;
import com.ruoyi.contract.domain.GeneratedContract;
import com.ruoyi.contract.domain.ParameterValue;
import com.ruoyi.contract.domain.TemplateVersionHistory;
import com.ruoyi.contract.domain.dto.InitiateDTO;
import com.ruoyi.contract.domain.dto.SaveContractDTO;
import com.ruoyi.contract.mapper.GeneratedContractMapper;
import com.ruoyi.contract.utils.OssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 合同生成服务
 *
 * <AUTHOR> Assistant
 * @date 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractGenerationService extends ServiceImpl<GeneratedContractMapper, GeneratedContract> {

    private final TemplateVersionHistoryService templateVersionHistoryService;
    private final ParamService paramService;
    private final ParameterValueService parameterValueService;
    private final ContractVersionHistoryService versionHistoryService;
    private final DocxMergeService docxMergeService;
    private final OssService ossService;
    private final ApplicationContext applicationContext;

    // 宏替换相关常量
    private static final Pattern SIMPLE_DATE_FORMAT_PATTERN = Pattern.compile("\\{\\{([yMdHmsSwWDFEazkKhGSZ\\-/:.,\\s]+)\\}\\}");
    private static final Pattern SPECIAL_DATE_PATTERN = Pattern.compile("\\{\\{specialDate\\}\\}");
    private static final String[] CHINESE_NUMBERS = {"〇", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

    /**
     * 对参数进行宏替换
     * 支持两种格式：
     * 1. SimpleDateFormat支持的格式，替换为当前日期
     * 2. {{specialDate}} 替换为二〇二五〇四〇一这种格式的当前日期
     *
     * @param textParams 文本参数映射
     * @param fileParams 文件参数映射
     */
    private void processMacroReplacements(Map<String, String> textParams, Map<String, byte[]> fileParams) {
        Date currentDate = new Date();
        LocalDate currentLocalDate = LocalDate.now();

        log.info("开始处理宏替换，当前日期: {}", DateUtil.format(currentDate, "yyyy-MM-dd"));

        // 处理文本参数的宏替换
        for (Map.Entry<String, String> entry : textParams.entrySet()) {
            String originalValue = entry.getValue();
            if (originalValue != null) {
                String processedValue = processMacros(originalValue, currentDate, currentLocalDate);
                if (!originalValue.equals(processedValue)) {
                    entry.setValue(processedValue);
                    log.debug("文本参数宏替换: {} -> {}", originalValue, processedValue);
                }
            }
        }

        // 处理文件参数的键名宏替换（如果文件参数的键中包含宏）
        List<String> keysToUpdate = new ArrayList<>();
        Map<String, byte[]> updatedFileParams = new HashMap<>();

        for (Map.Entry<String, byte[]> entry : fileParams.entrySet()) {
            String originalKey = entry.getKey();
            String processedKey = processMacros(originalKey, currentDate, currentLocalDate);

            if (!originalKey.equals(processedKey)) {
                keysToUpdate.add(originalKey);
                updatedFileParams.put(processedKey, entry.getValue());
                log.debug("文件参数键名宏替换: {} -> {}", originalKey, processedKey);
            }
        }

        // 更新文件参数的键名
        for (String oldKey : keysToUpdate) {
            fileParams.remove(oldKey);
        }
        fileParams.putAll(updatedFileParams);

        log.info("宏替换完成，处理了 {} 个文本参数，{} 个文件参数键名",
                textParams.size(), updatedFileParams.size());
    }

    /**
     * 处理单个字符串中的宏
     *
     * @param input 输入字符串
     * @param currentDate 当前日期
     * @param currentLocalDate 当前本地日期
     * @return 处理后的字符串
     */
    private String processMacros(String input, Date currentDate, LocalDate currentLocalDate) {
        if (input == null || input.trim().isEmpty()) {
            return input;
        }

        String result = input;

        // 处理特殊日期格式 {{specialDate}}
        result = processSpecialDateMacro(result, currentLocalDate);

        // 处理SimpleDateFormat格式的宏
        result = processDateFormatMacros(result, currentDate);

        return result;
    }

    /**
     * 处理特殊日期格式宏 {{specialDate}}
     * 将其替换为二〇二五〇四〇一这种格式
     */
    private String processSpecialDateMacro(String input, LocalDate currentDate) {
        Matcher matcher = SPECIAL_DATE_PATTERN.matcher(input);

        if (matcher.find()) {
            String specialDateStr = formatSpecialDate(currentDate);
            String result = matcher.replaceAll(specialDateStr);
            log.debug("特殊日期宏替换: {} -> {}", input, result);
            return result;
        }

        return input;
    }

    /**
     * 处理SimpleDateFormat格式的宏
     */
    private String processDateFormatMacros(String input, Date currentDate) {
        Matcher matcher = SIMPLE_DATE_FORMAT_PATTERN.matcher(input);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String datePattern = matcher.group(1);
            String formattedDate;

            try {
                // 预处理一些常见的格式，确保兼容性
                String processedPattern = preprocessDatePattern(datePattern);
                
                // 尝试使用SimpleDateFormat格式化日期
                SimpleDateFormat sdf = new SimpleDateFormat(processedPattern);
                formattedDate = sdf.format(currentDate);
                log.debug("日期格式宏替换: {} -> {} (处理后格式: {})", datePattern, formattedDate, processedPattern);
            } catch (Exception e) {
                // 如果格式无效，使用默认格式
                formattedDate = DateUtil.format(currentDate, "yyyy-MM-dd");
                log.warn("无效的日期格式 '{}', 使用默认格式: {}", datePattern, formattedDate, e);
            }

            matcher.appendReplacement(sb, formattedDate);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 预处理日期格式模式，处理一些常见的格式变体
     */
    private String preprocessDatePattern(String pattern) {
        if (pattern == null) {
            return "yyyy-MM-dd";
        }
        
        // 处理大写格式到小写格式的映射
        String processedPattern = pattern;
        
        // 常见的格式映射
        Map<String, String> formatMappings = new HashMap<>();
        formatMappings.put("YYYY", "yyyy");
        formatMappings.put("YY", "yy");
        formatMappings.put("MM", "MM");  // 月份保持不变
        formatMappings.put("DD", "dd");
        formatMappings.put("HH", "HH");
        formatMappings.put("mm", "mm");
        formatMappings.put("SS", "ss");
        
        // 替换格式映射
        for (Map.Entry<String, String> entry : formatMappings.entrySet()) {
            processedPattern = processedPattern.replace(entry.getKey(), entry.getValue());
        }
        
        // 验证并支持一些特殊的格式
        switch (processedPattern.toLowerCase()) {
            case "yyyymm":
                return "yyyyMM";
            case "yyyymmdd":
                return "yyyyMMdd";
            case "yyyy-mm":
                return "yyyy-MM";
            case "yyyy-mm-dd":
                return "yyyy-MM-dd";
            case "yyyy/mm/dd":
                return "yyyy/MM/dd";
            case "mm/dd/yyyy":
                return "MM/dd/yyyy";
            case "dd/mm/yyyy":
                return "dd/MM/yyyy";
            case "dd-mm-yyyy":
                return "dd-MM-yyyy";
            case "mm-dd-yyyy":
                return "MM-dd-yyyy";
            case "yyyy.mm.dd":
                return "yyyy.MM.dd";
            case "dd.mm.yyyy":
                return "dd.MM.yyyy";
            default:
                return processedPattern;
        }
    }

    /**
     * 将日期格式化为特殊的中文格式
     * 例如：2025-04-01 -> 二〇二五〇四〇一
     */
    private String formatSpecialDate(LocalDate date) {
        String dateStr = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        StringBuilder result = new StringBuilder();

        for (char c : dateStr.toCharArray()) {
            int digit = Character.getNumericValue(c);
            result.append(CHINESE_NUMBERS[digit]);
        }

        return result.toString();
    }

    /**
     * 发起合同（生成文件并保存）
     *
     * @param dto 发起请求DTO
     * @return 生成的合同ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long initiateContract(InitiateDTO dto) {
        try {
            // 1. 数据准备 - 获取模板最新版本
            TemplateVersionHistory templateVersion = templateVersionHistoryService.getLatestVersion(dto.getTemplateId());
            if (templateVersion == null) {
                throw new RuntimeException("模板不存在或没有版本");
            }

            List<ParameterValue> parameterValues = parameterValueService.getBySetId(dto.getParameterSetId());
            if (parameterValues.isEmpty()) {
                throw new RuntimeException("参数集不存在或为空");
            }

            // 3. 使用高格式要求的混合替换方案
            // 分离文本参数和文件参数
            Map<String, String> textParams = new HashMap<>();
            Map<String, byte[]> fileParams = new HashMap<>();

            for (ParameterValue value : parameterValues) {
                if (ParameterValue.VALUE_TYPE_TEXT.equals(value.getValueType())) {
                    // 文本参数
                    textParams.put(value.getParamName(), value.getParamValue());
                } else {
                    // 文件参数 - 下载并准备字节数组
                    String paramFilePath = value.getParamValue();
                    if (StringUtils.hasText(paramFilePath)) {
                        try (InputStream paramStream = ossService.downloadFile(paramFilePath)) {
                            byte[] paramFileBytes = readAllBytes(paramStream);
                            fileParams.put(value.getParamName(), paramFileBytes);
                            log.info("准备文件参数: {}, 大小: {} bytes", value.getParamName(), paramFileBytes.length);
                        } catch (Exception e) {
                            log.error("下载文件参数失败: {}, 路径: {}", value.getParamName(), paramFilePath, e);
                            throw new RuntimeException("下载文件参数失败: " + value.getParamName(), e);
                        }
                    }
                }
            }

            // 3.1 进行宏替换处理
            processMacroReplacements(textParams, fileParams);
            log.info("宏替换完成，文本参数: {} 项, 文件参数: {} 项", textParams.size(), fileParams.size());

            // 3.5 生成默认合同名称
            Map<String, String> paramMap = new HashMap<>();
            for (ParameterValue value : parameterValues) {
                // 对于文本参数直接使用值，对于文件参数使用HTML内容或文件路径
                String displayValue = ParameterValue.VALUE_TYPE_TEXT.equals(value.getValueType())
                    ? value.getParamValue()
                    : (StringUtils.hasText(value.getHtmlContent()) ? value.getHtmlContent() : value.getParamValue());
                paramMap.put(value.getParamName(), displayValue);
            }
            String defaultContractName = generateContractName(paramMap);

            // 下载主模板文件到字节数组
            byte[] currentDocxBytes;
            try (InputStream templateStream = ossService.downloadFile(templateVersion.getStoragePath())) {
                currentDocxBytes = readAllBytes(templateStream);
                log.info("模板文件下载完成，大小: {} bytes", currentDocxBytes.length);
            }

            // 4. 执行高格式要求的混合替换
            currentDocxBytes = docxMergeService.highQualityMixedReplace(currentDocxBytes, textParams, fileParams);
            log.info("高格式要求的混合替换完成，文本参数: {} 项, 文件参数: {} 项", textParams.size(), fileParams.size());

            // 5. 上传最终文件到临时目录
            String tempFilePath;
            try (ByteArrayInputStream finalContractStream = new ByteArrayInputStream(currentDocxBytes)) {
                tempFilePath = ossService.uploadFile(
                    finalContractStream,
                    defaultContractName + ".docx",
                    "temp/contracts"
                );
            }

            // 6. 直接保存合同
            String finalContractName = StringUtils.hasText(dto.getContractName()) ?
                dto.getContractName() : defaultContractName;

            Long contractId = saveContractFromTempFile(dto, templateVersion.getId(), tempFilePath, finalContractName);
            log.info("合同生成并保存成功，ID: {}, 名称: {}", contractId, finalContractName);
            return contractId;

        } catch (Exception e) {
            log.error("合同生成失败", e);
            throw new RuntimeException("合同生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从临时文件保存合同
     *
     * @param dto 发起请求DTO
     * @param templateVersionId 模板版本ID
     * @param tempFilePath 临时文件路径
     * @param contractName 合同名称
     * @return 生成的合同ID
     */
    private Long saveContractFromTempFile(InitiateDTO dto, Long templateVersionId, String tempFilePath, String contractName) {
        try {
            // 1. 创建生成合同记录
            GeneratedContract contract = new GeneratedContract();
            contract.setContractName(contractName);
            contract.setProductCode(dto.getProductCode());
            contract.setProductName(dto.getProductName());
            contract.setProductType(dto.getProductType());
            contract.setStatus(GeneratedContract.STATUS_PENDING);
            contract.setInvestmentManager(dto.getInvestmentManager());
            contract.setProductManager(dto.getProductManager());
            contract.setTemplateVersionId(templateVersionId);
            contract.setParameterSetId(dto.getParameterSetId());
            // TODO: 设置创建人信息

            save(contract);

            // 2. 将临时文件移动到永久存储位置
            InputStream tempFileStream = ossService.downloadFile(tempFilePath);
            String permanentPath = ossService.uploadFile(
                tempFileStream,
                contractName + ".docx",
                "contracts/generated"
            );
            tempFileStream.close();

            // 删除临时文件
            ossService.deleteFile(tempFilePath);

            // 3. 创建第一条合同文件版本历史记录
            ContractVersionHistory versionHistory = new ContractVersionHistory();
            versionHistory.setContractId(contract.getId());
            versionHistory.setVersion("1.0");
            versionHistory.setStoragePath(permanentPath);
            // TODO: 设置编辑人信息

            versionHistoryService.save(versionHistory);

            // 4. 更新合同的当前文件版本ID
            contract.setCurrentDocVersionId(versionHistory.getId());
            updateById(contract);

            return contract.getId();

        } catch (Exception e) {
            log.error("保存合同失败", e);
            throw new RuntimeException("保存合同失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存已发起的合同
     *
     * @param dto 保存合同DTO
     * @return 生成的合同ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveInitiatedContract(SaveContractDTO dto) {
        try {
            // 1. 创建生成合同记录
            GeneratedContract contract = new GeneratedContract();
            contract.setContractName(dto.getContractName());
            contract.setProductCode(dto.getProductCode());
            contract.setProductName(dto.getProductName());
            contract.setProductType(dto.getProductType());
            contract.setStatus(GeneratedContract.STATUS_PENDING);
            contract.setInvestmentManager(dto.getInvestmentManager());
            contract.setProductManager(dto.getProductManager());
            contract.setTemplateVersionId(dto.getTemplateVersionId());
            contract.setParameterSetId(dto.getParameterSetId());
            // TODO: 设置创建人信息
            // contract.setCreatorId(getCurrentUserId());
            // contract.setCreatorName(getCurrentUserName());

            save(contract);

            // 2. 将临时文件移动到永久目录并重命名
            InputStream tempFileStream = ossService.downloadFile(dto.getTempFilePath());
            String permanentPath = ossService.uploadFile(
                tempFileStream,
                dto.getContractName() + ".docx",
                "contracts"
            );
            tempFileStream.close();

            // 删除临时文件
            ossService.deleteFile(dto.getTempFilePath());

            // 3. 创建第一条合同文件版本历史记录
            ContractVersionHistory versionHistory = new ContractVersionHistory();
            versionHistory.setContractId(contract.getId());
            versionHistory.setVersion("1.0");
            versionHistory.setStoragePath(permanentPath);
            // TODO: 设置编辑人信息
            // versionHistory.setEditorId(getCurrentUserId());

            versionHistoryService.save(versionHistory);

            // 4. 更新合同的当前文件版本ID
            contract.setCurrentDocVersionId(versionHistory.getId());
            updateById(contract);

            log.info("合同保存成功，ID: {}, 名称: {}", contract.getId(), dto.getContractName());
            return contract.getId();

        } catch (Exception e) {
            log.error("保存合同失败", e);
            throw new RuntimeException("保存合同失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新合同状态
     *
     * @param generatedContractId 生成合同ID
     * @param status 新状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateContractStatus(Long generatedContractId, String status) {
        GeneratedContract contract = getById(generatedContractId);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        String oldStatus = contract.getStatus();
        contract.setStatus(status);
        updateById(contract);

        // 触发器逻辑：如果新状态是'审核通过'，则在合同管理模块创建记录
        if (GeneratedContract.STATUS_APPROVED.equals(status) &&
            !GeneratedContract.STATUS_APPROVED.equals(oldStatus)) {
            // 使用ApplicationContext延迟获取Bean，避免循环依赖
            ContractManagementService contractManagementService = applicationContext.getBean(ContractManagementService.class);
            contractManagementService.createFromGeneratedContract(generatedContractId);
        }

        log.info("合同状态更新成功，ID: {}, 状态: {} -> {}", generatedContractId, oldStatus, status);
    }

    /**
     * 分页查询合同列表（包含文件路径）
     *
     * @param current 当前页
     * @param size 页大小
     * @param contractName 合同名称（可选）
     * @param productName 产品名称（可选）
     * @param productCode 产品代码（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public IPage<Map<String, Object>> getContractList(Long current, Long size,
                                                     String contractName, String productName,
                                                     String productCode, String status) {
        Page<GeneratedContract> page = new Page<>(current, size);

        LambdaQueryWrapper<GeneratedContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(contractName), GeneratedContract::getContractName, contractName)
                   .like(StringUtils.hasText(productName), GeneratedContract::getProductName, productName)
                   .like(StringUtils.hasText(productCode), GeneratedContract::getProductCode, productCode)
                   .eq(StringUtils.hasText(status), GeneratedContract::getStatus, status)
                   .orderByDesc(GeneratedContract::getCreatedTime);

        IPage<GeneratedContract> contractPage = page(page, queryWrapper);

        // 转换为包含文件路径的结果
        Page<Map<String, Object>> resultPage = new Page<>(current, size);
        resultPage.setTotal(contractPage.getTotal());
        resultPage.setPages(contractPage.getPages());

        List<Map<String, Object>> records = new ArrayList<>();
        for (GeneratedContract contract : contractPage.getRecords()) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", String.valueOf(contract.getId()));
            record.put("contractName", contract.getContractName());
            record.put("productCode", contract.getProductCode());
            record.put("productName", contract.getProductName());
            record.put("productType", contract.getProductType());
            record.put("status", contract.getStatus());
            record.put("investmentManager", contract.getInvestmentManager());
            record.put("productManager", contract.getProductManager());
            record.put("creatorId", contract.getCreatorId());
            record.put("creatorName", contract.getCreatorName());
            record.put("templateVersionId", String.valueOf(contract.getTemplateVersionId()));
            record.put("parameterSetId", String.valueOf(contract.getParameterSetId()));
            record.put("currentDocVersionId", String.valueOf(contract.getCurrentDocVersionId()));

            record.put("createdTime", DateUtil.format(contract.getCreatedTime(), "yyyy-MM-dd HH:mm:ss"));
            record.put("updatedTime", DateUtil.format(contract.getUpdatedTime(), "yyyy-MM-dd HH:mm:ss"));

            // 获取当前版本的文件路径
            String filePath = null;
            String versionNum = null;
            if (contract.getCurrentDocVersionId() != null) {
                ContractVersionHistory version = versionHistoryService.getById(contract.getCurrentDocVersionId());
                if (version != null) {
                    filePath = version.getStoragePath();
                    versionNum = version.getVersion();
                }
            }
            record.put("filePath", filePath);
            record.put("version", versionNum);

            records.add(record);
        }

        resultPage.setRecords(records);
        return resultPage;
    }

    /**
     * 根据合同ID查询所有版本列表（包含模板ID）
     *
     * @param contractId 合同ID
     * @return 版本列表（包含模板ID字段）
     */
    public List<Map<String, Object>> getContractVersions(Long contractId) {
        // 1. 获取合同信息
        GeneratedContract contract = getById(contractId);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 2. 获取模板版本信息，从中获取模板ID
        TemplateVersionHistory templateVersion = templateVersionHistoryService.getById(contract.getTemplateVersionId());
        String templateId = templateVersion != null ? String.valueOf(templateVersion.getTemplateId()) : null;

        // 3. 获取所有版本历史
        List<ContractVersionHistory> versions = versionHistoryService.getVersionsByContractId(contractId);

        // 4. 转换为包含模板ID的Map格式
        List<Map<String, Object>> result = new ArrayList<>();
        for (ContractVersionHistory version : versions) {
            Map<String, Object> versionMap = new HashMap<>();
            versionMap.put("id", String.valueOf(version.getId()));
            versionMap.put("contractId", String.valueOf(version.getContractId()));
            versionMap.put("version", version.getVersion());
            versionMap.put("storagePath", version.getStoragePath());
            versionMap.put("editorId", version.getEditorId() != null ? String.valueOf(version.getEditorId()) : null);
            versionMap.put("editedTime", version.getEditedTime());
            versionMap.put("editedName", version.getEditorName());
            versionMap.put("templateId", templateId); // 添加模板ID字段
            result.add(versionMap);
        }

        return result;
    }

    /**
     * 删除合同（包括所有版本文件）
     *
     * @param contractId 合同ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteContract(Long contractId) {
        // 1. 检查合同是否存在
        GeneratedContract contract = getById(contractId);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 2. 获取所有版本历史
        List<ContractVersionHistory> versions = versionHistoryService.getVersionsByContractId(contractId);

        // 3. 删除所有版本文件
        for (ContractVersionHistory version : versions) {
            if (StringUtils.hasText(version.getStoragePath())) {
                try {
                    ossService.deleteFile(version.getStoragePath());
                    log.info("删除合同文件成功: {}", version.getStoragePath());
                } catch (Exception e) {
                    log.warn("删除合同文件失败: {}, 错误: {}", version.getStoragePath(), e.getMessage());
                }
            }
        }

        // 4. 删除版本历史记录
        if (!versions.isEmpty()) {
            List<Long> versionIds = versions.stream()
                .map(ContractVersionHistory::getId)
                .collect(Collectors.toList());
            versionHistoryService.removeByIds(versionIds);
            log.info("删除合同版本历史记录: {} 条", versionIds.size());
        }

        // 5. 删除合同记录
        removeById(contractId);
        log.info("删除合同记录成功: {}", contractId);
    }

    /**
     * 生成合同名称
     * 根据规则：${产品名称}${合同类型}-${产品托管人名称}
     *
     * @param paramMap 参数映射
     * @return 合同名称
     */
    private String generateContractName(Map<String, String> paramMap) {
        String productName = getParamDisplayValue(paramMap.get("产品名称"));
        String contractType = getParamDisplayValue(paramMap.get("合同类型"));
        String custodianName = getParamDisplayValue(paramMap.get("产品托管人名称"));

        return String.format("%s%s-%s",
            productName != null ? productName : "未知产品",
            contractType != null ? contractType : "未知类型",
            custodianName != null ? custodianName : "未知托管人"
        );
    }

    /**
     * 获取参数的显示值（从文件中提取文本内容）
     *
     * @param paramFilePath 参数文件路径
     * @return 显示值
     */
    private String getParamDisplayValue(String paramFilePath) {
        if (!StringUtils.hasText(paramFilePath)) {
            return null;
        }

        try {
            InputStream paramStream = ossService.downloadFile(paramFilePath);
            // 这里简化处理，实际应该解析Word文档内容
            // 可以使用Apache POI来提取文档文本
            paramStream.close();
            return "参数值"; // 简化返回
        } catch (Exception e) {
            log.warn("获取参数显示值失败: {}", paramFilePath, e);
            return null;
        }
    }

    /**
     * 清理临时目录
     *
     * @param tempDir 临时目录
     */
    private void cleanupTempDirectory(Path tempDir) {
        try {
            Files.walk(tempDir)
                 .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                 .forEach(path -> {
                     try {
                         Files.deleteIfExists(path);
                     } catch (Exception e) {
                         log.warn("删除临时文件失败: {}", path, e);
                     }
                 });
        } catch (Exception e) {
            log.warn("清理临时目录失败: {}", tempDir, e);
        }
    }

    /**
     * Java 8兼容的readAllBytes方法
     *
     * @param inputStream 输入流
     * @return 字节数组
     */
    private byte[] readAllBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[16384];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        return buffer.toByteArray();
    }
}
